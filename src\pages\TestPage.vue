<template>
  <div class="min-h-screen bg-theme-gradient py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              🧪 功能测试页面
            </span>
          </h1>
        </div>
        
        <div class="w-20"></div>
      </div>

      <!-- 测试区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 主题测试 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">🎨 主题测试</h2>
            
            <div class="space-y-4">
              <p class="text-sm text-gray-600">
                测试主题切换功能，观察背景颜色变化
              </p>
              
              <div class="flex flex-wrap gap-2">
                <button 
                  v-for="theme in themes" 
                  :key="theme.name"
                  class="btn btn-sm"
                  :class="currentTheme === theme.name ? 'btn-primary' : 'btn-outline'"
                  @click="switchTheme(theme.name)"
                >
                  {{ theme.icon }} {{ theme.displayName }}
                </button>
              </div>
              
              <div class="alert alert-info">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                  <h3 class="font-bold">当前主题</h3>
                  <div class="text-sm">{{ getCurrentThemeInfo()?.displayName || '未知' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模态框测试 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">📱 模态框测试</h2>
            
            <div class="space-y-4">
              <p class="text-sm text-gray-600">
                测试模态框的 z-index 层级是否正确
              </p>
              
              <div class="space-y-2">
                <button
                  class="btn btn-outline btn-sm w-full"
                  @click="showTestModal = true"
                >
                  打开测试模态框
                </button>

                <button
                  class="btn btn-outline btn-sm w-full"
                  @click="showNestedModal = true"
                >
                  打开嵌套模态框
                </button>

                <button
                  class="btn btn-outline btn-sm w-full"
                  @click="showDropdown = !showDropdown"
                >
                  切换下拉菜单
                </button>

                <button
                  class="btn btn-warning btn-sm w-full"
                  @click="createHighZIndexElement"
                >
                  创建高 z-index 干扰元素
                </button>
              </div>
              
              <!-- 下拉菜单测试 -->
              <div class="dropdown" :class="{ 'dropdown-open': showDropdown }">
                <div tabindex="0" role="button" class="btn btn-sm w-full">
                  下拉菜单测试
                </div>
                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[9998] w-full p-2 shadow">
                  <li><a @click="showDropdown = false">选项 1</a></li>
                  <li><a @click="showDropdown = false">选项 2</a></li>
                  <li><a @click="showDropdown = false">选项 3</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 无障碍测试 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">♿ 无障碍测试</h2>
            
            <div class="space-y-4">
              <p class="text-sm text-gray-600">
                测试无障碍功能和键盘导航
              </p>
              
              <div class="space-y-2">
                <button 
                  class="btn btn-outline btn-sm w-full"
                  @click="testKeyboardNavigation"
                >
                  测试键盘导航
                </button>
                
                <button 
                  class="btn btn-outline btn-sm w-full"
                  @click="testScreenReader"
                >
                  测试屏幕阅读器
                </button>
                
                <button 
                  class="btn btn-outline btn-sm w-full"
                  @click="toggleHighContrast"
                >
                  切换高对比度
                </button>
              </div>
              
              <div class="alert alert-success">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                  <h3 class="font-bold">快捷键提示</h3>
                  <div class="text-sm">按 ? 键打开帮助，Ctrl+T 切换主题</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 组件集成测试 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">🔧 组件集成测试</h2>
            
            <div class="space-y-4">
              <p class="text-sm text-gray-600">
                测试各个组件的集成效果
              </p>
              
              <div class="flex flex-wrap gap-2">
                <!-- 主题切换器 -->
                <ThemeSwitcher show-label />
                
                <!-- 无障碍设置 -->
                <AccessibilitySettings />
                
                <!-- 快捷键帮助 -->
                <ShortcutHelp />
              </div>
              
              <div class="alert alert-warning">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div>
                  <h3 class="font-bold">测试说明</h3>
                  <div class="text-sm">点击各个按钮测试功能，观察模态框是否正确显示在最前面</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试模态框 -->
      <div v-if="showTestModal" class="modal modal-open test-modal-1">
        <div class="modal-box test-modal-box-1">
          <h3 class="font-bold text-lg">测试模态框</h3>
          <p class="py-4">这是一个测试模态框，用于验证 z-index 是否正确。</p>
          <div class="alert alert-info mb-4">
            <div class="text-sm">
              <div>当前 z-index: 99999</div>
              <div>如果能看到这个模态框，说明 z-index 正常工作</div>
            </div>
          </div>
          <div class="modal-action">
            <button class="btn" @click="showTestModal = false">关闭</button>
            <button class="btn btn-primary" @click="showNestedModal = true">打开嵌套模态框</button>
          </div>
        </div>
      </div>

      <!-- 嵌套模态框 -->
      <div v-if="showNestedModal" class="modal modal-open test-modal-2">
        <div class="modal-box test-modal-box-2">
          <h3 class="font-bold text-lg">嵌套模态框</h3>
          <p class="py-4">这是一个嵌套的模态框，应该显示在前一个模态框之上。</p>
          <div class="alert alert-success mb-4">
            <div class="text-sm">
              <div>当前 z-index: 100001</div>
              <div>这个模态框应该在第一个模态框之上</div>
            </div>
          </div>
          <div class="modal-action">
            <button class="btn" @click="showNestedModal = false">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ThemeManager, THEMES } from '../utils/theme.js'
import { AccessibilityManager } from '../utils/accessibility.js'
import ThemeSwitcher from '../components/ThemeSwitcher.vue'
import AccessibilitySettings from '../components/AccessibilitySettings.vue'
import ShortcutHelp from '../components/ShortcutHelp.vue'

const router = useRouter()

// 响应式数据
const currentTheme = ref('light')
const showTestModal = ref(false)
const showNestedModal = ref(false)
const showDropdown = ref(false)
const themes = THEMES

// 方法
const switchTheme = (themeName) => {
  currentTheme.value = themeName
  ThemeManager.setTheme(themeName)
  
  // 更新页面背景
  setTimeout(() => {
    const pageContainer = document.querySelector('.bg-theme-gradient')
    if (pageContainer) {
      pageContainer.className = pageContainer.className.replace(/theme-\w+/g, '')
      pageContainer.classList.add(`theme-${themeName}`)
    }
  }, 50)
}

const getCurrentThemeInfo = () => {
  return THEMES.find(theme => theme.name === currentTheme.value)
}

const testKeyboardNavigation = () => {
  alert('键盘导航测试：请使用 Tab 键在元素间切换，Enter 键激活按钮')
}

const testScreenReader = () => {
  AccessibilityManager.announceToScreenReader('这是一个屏幕阅读器测试消息')
  alert('屏幕阅读器测试消息已发送')
}

const toggleHighContrast = () => {
  AccessibilityManager.toggleHighContrast()
}

const createHighZIndexElement = () => {
  // 创建一个高 z-index 的干扰元素来测试模态框是否能正确显示在最前面
  const interferenceElement = document.createElement('div')
  interferenceElement.id = 'z-index-interference'
  interferenceElement.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 200px;
    background: rgba(255, 0, 0, 0.8);
    z-index: 50000;
    border: 3px solid red;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
  `
  interferenceElement.innerHTML = `
    <div>
      <div>干扰元素 (z-index: 50000)</div>
      <div style="font-size: 12px; margin-top: 10px;">点击移除</div>
    </div>
  `

  interferenceElement.addEventListener('click', () => {
    document.body.removeChild(interferenceElement)
  })

  document.body.appendChild(interferenceElement)

  // 5秒后自动移除
  setTimeout(() => {
    if (document.getElementById('z-index-interference')) {
      document.body.removeChild(interferenceElement)
    }
  }, 5000)
}

// 组件挂载时初始化
onMounted(() => {
  currentTheme.value = ThemeManager.getCurrentTheme()
  
  // 确保页面背景正确应用
  setTimeout(() => {
    const pageContainer = document.querySelector('.bg-theme-gradient')
    if (pageContainer) {
      pageContainer.classList.add(`theme-${currentTheme.value}`)
    }
  }, 100)
})
</script>

<style scoped>
/* 确保测试页面的模态框正确显示 */
.test-modal-1 {
  z-index: 99999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

.test-modal-box-1 {
  z-index: 100000 !important;
  position: relative !important;
}

.test-modal-2 {
  z-index: 100001 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

.test-modal-box-2 {
  z-index: 100002 !important;
  position: relative !important;
}

/* 通用模态框样式 */
.modal {
  z-index: 99999 !important;
}

.modal.modal-open {
  z-index: 99999 !important;
}

.modal-box {
  z-index: 100000 !important;
}
</style>
