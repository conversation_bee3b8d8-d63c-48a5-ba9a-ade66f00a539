# 🚀 MBTI Friends 部署指南

本指南将帮助您将 MBTI Friends 应用部署到腾讯云 CloudBase。

## 📋 部署前准备

### 1. 环境要求
- Node.js 16+
- npm 或 yarn
- 腾讯云账号
- CloudBase CLI 工具

### 2. 安装 CloudBase CLI
```bash
npm install -g @cloudbase/cli
```

### 3. 登录 CloudBase
```bash
tcb login
```

## 🔧 配置环境

### 1. 创建 CloudBase 环境
1. 访问 [CloudBase 控制台](https://console.cloud.tencent.com/tcb)
2. 创建新环境或选择现有环境
3. 记录环境 ID（格式如：`your-env-id-xxxxx`）

### 2. 配置项目
1. 复制配置模板：
   ```bash
   cp cloudbaserc.template.json cloudbaserc.json
   cp .env.template .env.local
   ```

2. 编辑 `cloudbaserc.json`：
   ```json
   {
     "envId": "YOUR_ACTUAL_ENV_ID"
   }
   ```

3. 编辑 `.env.local`：
   ```env
   VITE_CLOUDBASE_ENV_ID=YOUR_ACTUAL_ENV_ID
   ```

## 🏗️ 构建和部署

### 1. 安装依赖
```bash
npm install
```

### 2. 构建生产版本
```bash
npm run build
```

### 3. 部署云函数
```bash
# 部署所有云函数
tcb functions:deploy --dir cloudfunctions

# 或者单独部署
tcb functions:deploy user-management --dir cloudfunctions/user-management
tcb functions:deploy mbti-match --dir cloudfunctions/mbti-match
tcb functions:deploy social-interaction --dir cloudfunctions/social-interaction
tcb functions:deploy data-statistics --dir cloudfunctions/data-statistics
```

### 4. 创建数据库集合
```bash
tcb db:createCollection users
tcb db:createCollection follows
tcb db:createCollection conversations
tcb db:createCollection messages
tcb db:createCollection visitors
tcb db:createCollection match_history
```

### 5. 部署静态网站
```bash
# 部署到根目录
tcb hosting:deploy dist

# 或部署到子目录
tcb hosting:deploy dist -e YOUR_ENV_ID --path /mbti-friends
```

## 🔐 安全配置

### 1. 数据库安全规则
在 CloudBase 控制台设置数据库权限：

```javascript
// users 集合权限
{
  "read": "auth != null",
  "write": "auth != null && auth.uid == resource.data._openid"
}

// 其他集合类似配置
```

### 2. 云函数权限
确保云函数有适当的权限访问数据库和其他服务。

### 3. 域名配置
在控制台配置安全域名，允许您的域名访问 CloudBase 服务。

## 📊 监控和维护

### 1. 查看部署状态
```bash
# 查看云函数列表
tcb functions:list

# 查看静态托管状态
tcb hosting:detail

# 查看数据库集合
tcb db:collection:list
```

### 2. 查看日志
```bash
# 查看云函数日志
tcb functions:log user-management

# 实时日志
tcb functions:log user-management --tail
```

### 3. 更新部署
```bash
# 更新云函数
tcb functions:deploy user-management --dir cloudfunctions/user-management

# 更新静态网站
npm run build
tcb hosting:deploy dist
```

## 🌐 访问应用

部署完成后，您可以通过以下方式访问应用：

1. **默认域名**：`https://YOUR_ENV_ID.tcloudbaseapp.com`
2. **自定义路径**：`https://YOUR_ENV_ID.tcloudbaseapp.com/mbti-friends`
3. **自定义域名**：在控制台配置自定义域名

## 🔧 环境变量说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `VITE_CLOUDBASE_ENV_ID` | CloudBase 环境 ID | `your-env-id-xxxxx` |
| `VITE_APP_NAME` | 应用名称 | `MBTI Friends` |
| `VITE_APP_VERSION` | 应用版本 | `1.0.0` |

## 🚨 常见问题

### 1. 云函数部署失败
- 检查 `package.json` 是否正确
- 确保函数代码没有语法错误
- 检查网络连接

### 2. 数据库权限错误
- 检查数据库安全规则
- 确保用户已正确登录
- 验证集合是否存在

### 3. 静态网站访问 404
- 检查构建输出目录
- 确保路由配置正确
- 验证部署路径

### 4. 跨域问题
- 在控制台配置安全域名
- 检查 CORS 设置
- 确保 API 调用正确

## 📞 技术支持

- [CloudBase 文档](https://cloud.tencent.com/document/product/876)
- [CloudBase 控制台](https://console.cloud.tencent.com/tcb)
- [GitHub Issues](https://github.com/your-repo/issues)

## 🔄 CI/CD 集成

### GitHub Actions 示例
```yaml
name: Deploy to CloudBase
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm install
        
      - name: Build
        run: npm run build
        
      - name: Deploy
        env:
          CLOUDBASE_SECRET_KEY: ${{ secrets.CLOUDBASE_SECRET_KEY }}
        run: |
          npm install -g @cloudbase/cli
          tcb login --key $CLOUDBASE_SECRET_KEY
          tcb functions:deploy --dir cloudfunctions
          tcb hosting:deploy dist
```

---

**部署完成后，记得删除或保护包含敏感信息的配置文件！** 🔒
