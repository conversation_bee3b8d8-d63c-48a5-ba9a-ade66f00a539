<template>
  <div class="min-h-screen bg-theme-gradient py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              👥 所有用户
            </span>
          </h1>
        </div>
        
        <button 
          v-if="currentUser"
          class="btn btn-outline btn-sm"
          @click="$router.push('/profile')"
        >
          我的资料
        </button>
        <button 
          v-else
          class="btn btn-primary btn-sm"
          @click="$router.push('/register')"
        >
          加入我们
        </button>
      </div>

      <!-- 筛选器和排序 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-lg mb-8">
        <div class="card-body">
          <!-- 第一行：基础筛选 -->
          <div class="flex flex-wrap gap-4 items-center mb-4">
            <!-- MBTI 类型筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">MBTI 类型</span>
              </label>
              <select
                v-model="filters.mbtiType"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applyFilters"
              >
                <option value="">全部类型</option>
                <option v-for="type in allMBTITypes" :key="type" :value="type">
                  {{ type }}
                </option>
              </select>
            </div>

            <!-- 年龄筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">年龄范围</span>
              </label>
              <select
                v-model="filters.ageRange"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applyFilters"
              >
                <option value="">全部年龄</option>
                <option value="18-25">18-25岁</option>
                <option value="26-30">26-30岁</option>
                <option value="31-35">31-35岁</option>
                <option value="36-40">36-40岁</option>
                <option value="40+">40岁以上</option>
              </select>
            </div>

            <!-- 性别筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">性别</span>
              </label>
              <select
                v-model="filters.gender"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applyFilters"
              >
                <option value="">全部性别</option>
                <option value="男">男</option>
                <option value="女">女</option>
                <option value="其他">其他</option>
              </select>
            </div>

            <!-- 在线状态筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">在线状态</span>
              </label>
              <select
                v-model="filters.onlineStatus"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applyFilters"
              >
                <option value="">全部状态</option>
                <option value="online">在线</option>
                <option value="offline">离线</option>
              </select>
            </div>
          </div>

          <!-- 第二行：高级筛选和排序 -->
          <div class="flex flex-wrap gap-4 items-center">
            <!-- 搜索框 -->
            <div class="form-control flex-1 min-w-64">
              <label class="label">
                <span class="label-text font-medium">搜索</span>
              </label>
              <SearchBox
                v-model="searchQuery"
                :users="allUsers"
                placeholder="搜索姓名、兴趣、标签..."
                input-class="input-sm"
                @search="handleSearch"
                @select="handleSearchSelect"
              />
            </div>

            <!-- 排序方式 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">排序方式</span>
              </label>
              <select
                v-model="sortBy"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applySorting"
              >
                <option value="default">默认排序</option>
                <option value="name">按姓名</option>
                <option value="age">按年龄</option>
                <option value="lastActive">按活跃度</option>
                <option value="compatibility">按匹配度</option>
                <option value="joinTime">按加入时间</option>
              </select>
            </div>

            <!-- 排序方向 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">排序方向</span>
              </label>
              <select
                v-model="sortOrder"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applySorting"
              >
                <option value="asc">升序</option>
                <option value="desc">降序</option>
              </select>
            </div>

            <!-- 视图模式 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">视图模式</span>
              </label>
              <div class="btn-group">
                <button
                  class="btn btn-sm"
                  :class="viewMode === 'grid' ? 'btn-active' : 'btn-outline'"
                  @click="viewMode = 'grid'"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                  </svg>
                </button>
                <button
                  class="btn btn-sm"
                  :class="viewMode === 'list' ? 'btn-active' : 'btn-outline'"
                  @click="viewMode = 'list'"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                  </svg>
                </button>
              </div>
            </div>

            <!-- 快捷筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">快捷筛选</span>
              </label>
              <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-outline btn-sm">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                  </svg>
                  筛选
                </div>
                <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                  <li><a @click="quickFilter('favorites')">我的收藏</a></li>
                  <li><a @click="quickFilter('following')">我的关注</a></li>
                  <li><a @click="quickFilter('highMatch')">高匹配度</a></li>
                  <li><a @click="quickFilter('online')">在线用户</a></li>
                  <li><a @click="quickFilter('newUsers')">新用户</a></li>
                </ul>
              </div>
            </div>

            <!-- 清除筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">&nbsp;</span>
              </label>
              <button
                class="btn btn-outline btn-sm"
                @click="clearFilters"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                清除
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户统计 -->
      <div class="stats stats-horizontal shadow-lg bg-white/70 backdrop-blur-sm mb-8">
        <div class="stat">
          <div class="stat-title">总用户数</div>
          <div class="stat-value text-primary">{{ allUsers.length }}</div>
        </div>
        <div class="stat">
          <div class="stat-title">筛选结果</div>
          <div class="stat-value text-secondary">{{ filteredUsers.length }}</div>
        </div>
        <div class="stat">
          <div class="stat-title">最受欢迎类型</div>
          <div class="stat-value text-accent text-lg">{{ mostPopularMBTI }}</div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div v-if="filteredUsers.length > 0">
        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="user in paginatedUsers"
            :key="user.id"
            class="card bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 relative"
            @click="viewUserProfile(user)"
          >
            <!-- 在线状态指示器 -->
            <div class="absolute top-4 right-4 z-10">
              <div
                class="w-3 h-3 rounded-full"
                :class="user.isOnline ? 'bg-green-500' : 'bg-gray-400'"
                :title="user.isOnline ? '在线' : '离线'"
              ></div>
            </div>

            <!-- 收藏按钮 -->
            <div v-if="currentUser && user.id !== currentUser.id" class="absolute top-4 left-4 z-10">
              <button
                class="btn btn-circle btn-xs"
                :class="isFavorited(user.id) ? 'btn-error text-white' : 'btn-ghost bg-white/80'"
                @click.stop="toggleFavorite(user.id)"
              >
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
              </button>
            </div>

            <div class="card-body">
              <!-- 用户头像和基本信息 -->
              <div class="flex items-center space-x-4 mb-4">
                <div class="avatar">
                  <div class="w-16 h-16 rounded-full ring ring-purple-200 ring-offset-2 relative">
                    <img :src="user.avatar" :alt="user.name" />
                    <!-- 在线状态小圆点 -->
                    <div
                      v-if="user.isOnline"
                      class="absolute bottom-0 right-0 w-4 h-4 bg-green-500 rounded-full border-2 border-white"
                    ></div>
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-bold text-gray-800">{{ user.name }}</h3>
                  <div class="flex items-center space-x-2 mt-1">
                    <span class="text-sm text-gray-600">{{ user.age }}岁</span>
                    <span class="text-sm text-gray-600">{{ user.gender }}</span>
                    <span
                      class="text-xs px-2 py-1 rounded-full"
                      :class="user.isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'"
                    >
                      {{ user.isOnline ? '在线' : getLastActiveText(user.lastActive) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- MBTI 类型 -->
              <div class="mb-4">
                <span
                  class="badge text-white text-sm px-3 py-2 w-full justify-center"
                  :class="getMBTIColor(user.mbtiType)"
                >
                  {{ user.mbtiType }} - {{ getMBTIName(user.mbtiType) }}
                </span>
              </div>

              <!-- 个人标签 -->
              <div v-if="user.tags && user.tags.length > 0" class="mb-4">
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="tag in user.tags.slice(0, 3)"
                    :key="tag"
                    class="badge badge-outline text-xs"
                  >
                    {{ tag }}
                  </span>
                  <span v-if="user.tags.length > 3" class="badge badge-ghost text-xs">
                    +{{ user.tags.length - 3 }}
                  </span>
                </div>
              </div>

              <!-- 自我介绍 -->
              <p v-if="user.bio" class="text-gray-600 text-sm mb-4 line-clamp-2">
                {{ user.bio }}
              </p>

              <!-- 兴趣爱好 -->
              <div class="mb-4">
                <h4 class="font-medium text-gray-700 mb-2 text-sm">兴趣爱好：</h4>
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="interest in user.interests.slice(0, 4)"
                    :key="interest"
                    class="badge badge-ghost text-xs"
                    :class="currentUser && currentUser.interests.includes(interest) ? 'badge-primary text-white' : ''"
                  >
                    {{ interest }}
                    <span v-if="currentUser && currentUser.interests.includes(interest)" class="ml-1">✨</span>
                  </span>
                  <span v-if="user.interests.length > 4" class="badge badge-ghost text-xs">
                    +{{ user.interests.length - 4 }}
                  </span>
                </div>
              </div>

              <!-- 匹配度（如果有当前用户） -->
              <div v-if="currentUser && user.id !== currentUser.id" class="mb-4">
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium text-gray-700">匹配度：</span>
                  <div class="flex items-center space-x-2">
                    <div
                      class="radial-progress text-primary text-sm"
                      :style="`--value:${getCompatibilityScore(user)}; --size:2rem; --thickness: 3px;`"
                    >
                      {{ getCompatibilityScore(user) }}%
                    </div>
                  </div>
                </div>
              </div>

              <!-- 社交统计 -->
              <div v-if="currentUser && user.id !== currentUser.id" class="mb-4">
                <div class="flex justify-between text-xs text-gray-500">
                  <span>关注 {{ getFollowingCount(user.id) }}</span>
                  <span>粉丝 {{ getFollowersCount(user.id) }}</span>
                  <span>{{ isFollowing(user.id) ? '已关注' : '' }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="card-actions justify-end" @click.stop>
                <button
                  v-if="currentUser && user.id !== currentUser.id"
                  class="btn btn-outline btn-sm"
                  @click="sendMessage(user)"
                >
                  💬 发消息
                </button>
                <button
                  v-if="currentUser && user.id !== currentUser.id"
                  class="btn btn-sm"
                  :class="isFollowing(user.id) ? 'btn-ghost' : 'btn-primary'"
                  @click="toggleFollow(user.id)"
                >
                  {{ isFollowing(user.id) ? '✓ 已关注' : '⭐ 关注' }}
                </button>
                <button
                  v-if="user.id === currentUser?.id"
                  class="btn btn-ghost btn-sm"
                  @click="$router.push('/profile')"
                >
                  ✏️ 编辑资料
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="space-y-4">
          <div
            v-for="user in paginatedUsers"
            :key="user.id"
            class="card bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
            @click="viewUserProfile(user)"
          >
            <div class="card-body p-6">
              <div class="flex items-center space-x-6">
                <!-- 头像 -->
                <div class="avatar">
                  <div class="w-20 h-20 rounded-full ring ring-purple-200 ring-offset-2 relative">
                    <img :src="user.avatar" :alt="user.name" />
                    <div
                      v-if="user.isOnline"
                      class="absolute bottom-0 right-0 w-5 h-5 bg-green-500 rounded-full border-2 border-white"
                    ></div>
                  </div>
                </div>

                <!-- 基本信息 -->
                <div class="flex-1">
                  <div class="flex items-center space-x-4 mb-2">
                    <h3 class="text-xl font-bold text-gray-800">{{ user.name }}</h3>
                    <span
                      class="badge text-white text-sm px-3 py-2"
                      :class="getMBTIColor(user.mbtiType)"
                    >
                      {{ user.mbtiType }}
                    </span>
                    <span class="text-sm text-gray-600">{{ user.age }}岁 · {{ user.gender }}</span>
                    <span
                      class="text-xs px-2 py-1 rounded-full"
                      :class="user.isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'"
                    >
                      {{ user.isOnline ? '在线' : getLastActiveText(user.lastActive) }}
                    </span>
                  </div>

                  <p v-if="user.bio" class="text-gray-600 text-sm mb-3 line-clamp-2">
                    {{ user.bio }}
                  </p>

                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="interest in user.interests.slice(0, 6)"
                      :key="interest"
                      class="badge badge-ghost text-xs"
                      :class="currentUser && currentUser.interests.includes(interest) ? 'badge-primary text-white' : ''"
                    >
                      {{ interest }}
                    </span>
                  </div>
                </div>

                <!-- 匹配度和操作 -->
                <div class="text-center space-y-3" @click.stop>
                  <div v-if="currentUser && user.id !== currentUser.id">
                    <div
                      class="radial-progress text-primary text-lg font-bold"
                      :style="`--value:${getCompatibilityScore(user)}; --size:4rem; --thickness: 6px;`"
                    >
                      {{ getCompatibilityScore(user) }}%
                    </div>
                    <div class="text-xs text-gray-500 mt-1">匹配度</div>
                  </div>

                  <div class="flex space-x-2">
                    <button
                      v-if="currentUser && user.id !== currentUser.id"
                      class="btn btn-circle btn-sm"
                      :class="isFavorited(user.id) ? 'btn-error text-white' : 'btn-ghost'"
                      @click="toggleFavorite(user.id)"
                    >
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                      </svg>
                    </button>
                    <button
                      v-if="currentUser && user.id !== currentUser.id"
                      class="btn btn-sm"
                      :class="isFollowing(user.id) ? 'btn-ghost' : 'btn-primary'"
                      @click="toggleFollow(user.id)"
                    >
                      {{ isFollowing(user.id) ? '✓' : '⭐' }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无结果 -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">🔍</div>
        <h3 class="text-2xl font-bold text-gray-600 mb-4">没有找到匹配的用户</h3>
        <p class="text-gray-500 mb-6">尝试调整筛选条件或清除所有筛选</p>
        <button 
          class="btn btn-primary"
          @click="clearFilters"
        >
          清除筛选条件
        </button>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="flex justify-center mt-8">
        <div class="join">
          <button 
            class="join-item btn btn-sm"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            «
          </button>
          <button 
            v-for="page in visiblePages" 
            :key="page"
            class="join-item btn btn-sm"
            :class="{ 'btn-active': page === currentPage }"
            @click="currentPage = page"
          >
            {{ page }}
          </button>
          <button 
            class="join-item btn btn-sm"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            »
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import LocalStorageManager from '../utils/localStorage.js'
import { getMBTIColor, getMBTIName, getAllMBTITypes } from '../utils/mbtiTypes.js'
import SearchBox from '../components/SearchBox.vue'

const router = useRouter()

// 响应式数据
const currentUser = ref(null)
const allUsers = ref([])
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = 9
const viewMode = ref('grid') // 'grid' 或 'list'
const sortBy = ref('default')
const sortOrder = ref('desc')

// 筛选条件
const filters = ref({
  mbtiType: '',
  ageRange: '',
  gender: '',
  onlineStatus: ''
})

// 获取所有 MBTI 类型
const allMBTITypes = getAllMBTITypes()

// 计算属性
const filteredUsers = computed(() => {
  let users = allUsers.value

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    users = users.filter(user =>
      user.name.toLowerCase().includes(query) ||
      user.interests.some(interest => interest.toLowerCase().includes(query)) ||
      (user.tags && user.tags.some(tag => tag.toLowerCase().includes(query))) ||
      (user.bio && user.bio.toLowerCase().includes(query))
    )
  }

  // MBTI 类型筛选
  if (filters.value.mbtiType) {
    users = users.filter(user => user.mbtiType === filters.value.mbtiType)
  }

  // 年龄筛选
  if (filters.value.ageRange) {
    const [min, max] = filters.value.ageRange.split('-').map(Number)
    if (max) {
      users = users.filter(user => user.age >= min && user.age <= max)
    } else {
      users = users.filter(user => user.age >= min)
    }
  }

  // 性别筛选
  if (filters.value.gender) {
    users = users.filter(user => user.gender === filters.value.gender)
  }

  // 在线状态筛选
  if (filters.value.onlineStatus) {
    if (filters.value.onlineStatus === 'online') {
      users = users.filter(user => user.isOnline)
    } else if (filters.value.onlineStatus === 'offline') {
      users = users.filter(user => !user.isOnline)
    }
  }

  return users
})

// 排序后的用户列表
const sortedUsers = computed(() => {
  let users = [...filteredUsers.value]

  if (sortBy.value === 'default') {
    return users
  }

  users.sort((a, b) => {
    let aValue, bValue

    switch (sortBy.value) {
      case 'name':
        aValue = a.name
        bValue = b.name
        break
      case 'age':
        aValue = a.age
        bValue = b.age
        break
      case 'lastActive':
        aValue = new Date(a.lastActive || a.createdAt)
        bValue = new Date(b.lastActive || b.createdAt)
        break
      case 'compatibility':
        if (!currentUser.value) return 0
        aValue = getCompatibilityScore(a)
        bValue = getCompatibilityScore(b)
        break
      case 'joinTime':
        aValue = new Date(a.createdAt)
        bValue = new Date(b.createdAt)
        break
      default:
        return 0
    }

    if (typeof aValue === 'string') {
      return sortOrder.value === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    } else {
      return sortOrder.value === 'asc'
        ? aValue - bValue
        : bValue - aValue
    }
  })

  return users
})

const totalPages = computed(() => Math.ceil(sortedUsers.value.length / pageSize))

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return sortedUsers.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

const mostPopularMBTI = computed(() => {
  if (allUsers.value.length === 0) return 'N/A'
  
  const counts = {}
  allUsers.value.forEach(user => {
    counts[user.mbtiType] = (counts[user.mbtiType] || 0) + 1
  })
  
  return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b)
})

// 方法
const applyFilters = () => {
  currentPage.value = 1
}

const applySorting = () => {
  currentPage.value = 1
}

const clearFilters = () => {
  filters.value = {
    mbtiType: '',
    ageRange: '',
    gender: '',
    onlineStatus: ''
  }
  searchQuery.value = ''
  sortBy.value = 'default'
  sortOrder.value = 'desc'
  currentPage.value = 1
}

const quickFilter = (type) => {
  clearFilters()

  switch (type) {
    case 'favorites':
      if (currentUser.value) {
        const favorites = LocalStorageManager.getFavorites(currentUser.value.id)
        allUsers.value = allUsers.value.filter(user => favorites.includes(user.id))
      }
      break
    case 'following':
      if (currentUser.value) {
        const following = LocalStorageManager.getFollowing(currentUser.value.id)
        allUsers.value = allUsers.value.filter(user => following.includes(user.id))
      }
      break
    case 'highMatch':
      if (currentUser.value) {
        sortBy.value = 'compatibility'
        sortOrder.value = 'desc'
      }
      break
    case 'online':
      filters.value.onlineStatus = 'online'
      break
    case 'newUsers':
      sortBy.value = 'joinTime'
      sortOrder.value = 'desc'
      break
  }

  applyFilters()
}

const getCompatibilityScore = (user) => {
  if (!currentUser.value) return 0

  const matches = LocalStorageManager.getMatches(currentUser.value, 100)
  const match = matches.find(m => m.id === user.id)
  return match ? match.matchScore : 0
}

// 收藏功能
const isFavorited = (userId) => {
  if (!currentUser.value) return false
  return LocalStorageManager.isFavorited(currentUser.value.id, userId)
}

const toggleFavorite = (userId) => {
  if (!currentUser.value) return

  if (isFavorited(userId)) {
    LocalStorageManager.removeFavorite(currentUser.value.id, userId)
  } else {
    LocalStorageManager.addFavorite(currentUser.value.id, userId)
  }
}

// 关注功能
const isFollowing = (userId) => {
  if (!currentUser.value) return false
  return LocalStorageManager.isFollowing(currentUser.value.id, userId)
}

const toggleFollow = (userId) => {
  if (!currentUser.value) return

  if (isFollowing(userId)) {
    LocalStorageManager.unfollowUser(currentUser.value.id, userId)
  } else {
    LocalStorageManager.followUser(currentUser.value.id, userId)
  }
}

const getFollowingCount = (userId) => {
  return LocalStorageManager.getFollowing(userId).length
}

const getFollowersCount = (userId) => {
  return LocalStorageManager.getFollowers(userId).length
}

// 时间格式化
const getLastActiveText = (lastActive) => {
  if (!lastActive) return '很久之前'

  const now = new Date()
  const lastActiveDate = new Date(lastActive)
  const diffMs = now - lastActiveDate
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  return '很久之前'
}

// 查看用户资料
const viewUserProfile = (user) => {
  if (currentUser.value && user.id !== currentUser.value.id) {
    // 记录访客
    LocalStorageManager.recordVisitor(user.id, currentUser.value.id)
  }

  // 这里可以跳转到用户详情页面
  // router.push(`/user/${user.id}`)
  console.log('查看用户资料:', user.name)
}

// 搜索处理
const handleSearch = (query) => {
  searchQuery.value = query
  applyFilters()
}

const handleSearchSelect = (suggestion) => {
  if (suggestion.type === 'user' && suggestion.user) {
    // 如果选择的是用户，可以直接查看其资料
    viewUserProfile(suggestion.user)
  } else {
    // 其他类型的建议，执行搜索
    handleSearch(suggestion.text)
  }
}

// 发送消息
const sendMessage = (user) => {
  if (!currentUser.value) return

  // 跳转到消息页面并预选用户
  router.push({
    path: '/messages',
    query: { userId: user.id }
  })
}

// 组件挂载时初始化
onMounted(() => {
  currentUser.value = LocalStorageManager.getCurrentUser()

  // 重新获取所有用户数据（包含新增的字段）
  const users = LocalStorageManager.getAllUsers()

  // 为没有新字段的用户添加默认值
  allUsers.value = users.map(user => ({
    ...user,
    socialLinks: user.socialLinks || {},
    tags: user.tags || [],
    lastActive: user.lastActive || user.createdAt,
    isOnline: user.isOnline !== undefined ? user.isOnline : Math.random() > 0.5
  }))

  // 如果用户数据需要更新，保存回本地存储
  const needsUpdate = users.some(user =>
    !user.socialLinks || !user.tags || !user.lastActive || user.isOnline === undefined
  )

  if (needsUpdate) {
    allUsers.value.forEach(user => {
      LocalStorageManager.updateUser(user.id, {
        socialLinks: user.socialLinks,
        tags: user.tags,
        lastActive: user.lastActive,
        isOnline: user.isOnline
      })
    })
  }
})
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
