<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-100 py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              👥 所有用户
            </span>
          </h1>
        </div>
        
        <button 
          v-if="currentUser"
          class="btn btn-outline btn-sm"
          @click="$router.push('/profile')"
        >
          我的资料
        </button>
        <button 
          v-else
          class="btn btn-primary btn-sm"
          @click="$router.push('/register')"
        >
          加入我们
        </button>
      </div>

      <!-- 筛选器 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-lg mb-8">
        <div class="card-body">
          <div class="flex flex-wrap gap-4 items-center">
            <!-- MBTI 类型筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">MBTI 类型</span>
              </label>
              <select 
                v-model="filters.mbtiType"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applyFilters"
              >
                <option value="">全部类型</option>
                <option v-for="type in allMBTITypes" :key="type" :value="type">
                  {{ type }}
                </option>
              </select>
            </div>

            <!-- 年龄筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">年龄范围</span>
              </label>
              <select 
                v-model="filters.ageRange"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applyFilters"
              >
                <option value="">全部年龄</option>
                <option value="18-25">18-25岁</option>
                <option value="26-30">26-30岁</option>
                <option value="31-35">31-35岁</option>
                <option value="36-40">36-40岁</option>
                <option value="40+">40岁以上</option>
              </select>
            </div>

            <!-- 性别筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">性别</span>
              </label>
              <select 
                v-model="filters.gender"
                class="select select-bordered select-sm w-full max-w-xs"
                @change="applyFilters"
              >
                <option value="">全部性别</option>
                <option value="男">男</option>
                <option value="女">女</option>
                <option value="其他">其他</option>
              </select>
            </div>

            <!-- 搜索框 -->
            <div class="form-control flex-1">
              <label class="label">
                <span class="label-text font-medium">搜索</span>
              </label>
              <input 
                type="text"
                v-model="searchQuery"
                placeholder="搜索姓名或兴趣..."
                class="input input-bordered input-sm w-full"
                @input="applyFilters"
              />
            </div>

            <!-- 清除筛选 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">&nbsp;</span>
              </label>
              <button 
                class="btn btn-outline btn-sm"
                @click="clearFilters"
              >
                清除筛选
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户统计 -->
      <div class="stats stats-horizontal shadow-lg bg-white/70 backdrop-blur-sm mb-8">
        <div class="stat">
          <div class="stat-title">总用户数</div>
          <div class="stat-value text-primary">{{ allUsers.length }}</div>
        </div>
        <div class="stat">
          <div class="stat-title">筛选结果</div>
          <div class="stat-value text-secondary">{{ filteredUsers.length }}</div>
        </div>
        <div class="stat">
          <div class="stat-title">最受欢迎类型</div>
          <div class="stat-value text-accent text-lg">{{ mostPopularMBTI }}</div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div v-if="filteredUsers.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div 
          v-for="user in paginatedUsers" 
          :key="user.id"
          class="card bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        >
          <div class="card-body">
            <!-- 用户头像和基本信息 -->
            <div class="flex items-center space-x-4 mb-4">
              <div class="avatar">
                <div class="w-16 h-16 rounded-full ring ring-purple-200 ring-offset-2">
                  <img :src="user.avatar" :alt="user.name" />
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-gray-800">{{ user.name }}</h3>
                <div class="flex items-center space-x-2 mt-1">
                  <span class="text-sm text-gray-600">{{ user.age }}岁</span>
                  <span class="text-sm text-gray-600">{{ user.gender }}</span>
                </div>
              </div>
            </div>

            <!-- MBTI 类型 -->
            <div class="mb-4">
              <span 
                class="badge text-white text-sm px-3 py-2 w-full justify-center"
                :class="getMBTIColor(user.mbtiType)"
              >
                {{ user.mbtiType }} - {{ getMBTIName(user.mbtiType) }}
              </span>
            </div>

            <!-- 自我介绍 -->
            <p v-if="user.bio" class="text-gray-600 text-sm mb-4 line-clamp-3">
              {{ user.bio }}
            </p>

            <!-- 兴趣爱好 -->
            <div class="mb-4">
              <h4 class="font-medium text-gray-700 mb-2 text-sm">兴趣爱好：</h4>
              <div class="flex flex-wrap gap-1">
                <span 
                  v-for="interest in user.interests.slice(0, 4)" 
                  :key="interest"
                  class="badge badge-ghost text-xs"
                  :class="currentUser && currentUser.interests.includes(interest) ? 'badge-primary text-white' : ''"
                >
                  {{ interest }}
                  <span v-if="currentUser && currentUser.interests.includes(interest)" class="ml-1">✨</span>
                </span>
                <span v-if="user.interests.length > 4" class="badge badge-ghost text-xs">
                  +{{ user.interests.length - 4 }}
                </span>
              </div>
            </div>

            <!-- 匹配度（如果有当前用户） -->
            <div v-if="currentUser && user.id !== currentUser.id" class="mb-4">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">匹配度：</span>
                <div class="flex items-center space-x-2">
                  <div 
                    class="radial-progress text-primary text-sm"
                    :style="`--value:${getCompatibilityScore(user)}; --size:2rem; --thickness: 3px;`"
                  >
                    {{ getCompatibilityScore(user) }}%
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions justify-end">
              <button 
                v-if="currentUser && user.id !== currentUser.id"
                class="btn btn-outline btn-sm"
              >
                💬 发消息
              </button>
              <button 
                v-if="currentUser && user.id !== currentUser.id"
                class="btn btn-primary btn-sm"
              >
                ⭐ 关注
              </button>
              <button 
                v-if="user.id === currentUser?.id"
                class="btn btn-ghost btn-sm"
                @click="$router.push('/profile')"
              >
                ✏️ 编辑资料
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 无结果 -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">🔍</div>
        <h3 class="text-2xl font-bold text-gray-600 mb-4">没有找到匹配的用户</h3>
        <p class="text-gray-500 mb-6">尝试调整筛选条件或清除所有筛选</p>
        <button 
          class="btn btn-primary"
          @click="clearFilters"
        >
          清除筛选条件
        </button>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="flex justify-center mt-8">
        <div class="join">
          <button 
            class="join-item btn btn-sm"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            «
          </button>
          <button 
            v-for="page in visiblePages" 
            :key="page"
            class="join-item btn btn-sm"
            :class="{ 'btn-active': page === currentPage }"
            @click="currentPage = page"
          >
            {{ page }}
          </button>
          <button 
            class="join-item btn btn-sm"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            »
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import LocalStorageManager from '../utils/localStorage.js'
import { getMBTIColor, getMBTIName, getAllMBTITypes } from '../utils/mbtiTypes.js'

// 响应式数据
const currentUser = ref(null)
const allUsers = ref([])
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = 9

// 筛选条件
const filters = ref({
  mbtiType: '',
  ageRange: '',
  gender: ''
})

// 获取所有 MBTI 类型
const allMBTITypes = getAllMBTITypes()

// 计算属性
const filteredUsers = computed(() => {
  let users = allUsers.value

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    users = users.filter(user => 
      user.name.toLowerCase().includes(query) ||
      user.interests.some(interest => interest.toLowerCase().includes(query))
    )
  }

  // MBTI 类型筛选
  if (filters.value.mbtiType) {
    users = users.filter(user => user.mbtiType === filters.value.mbtiType)
  }

  // 年龄筛选
  if (filters.value.ageRange) {
    const [min, max] = filters.value.ageRange.split('-').map(Number)
    if (max) {
      users = users.filter(user => user.age >= min && user.age <= max)
    } else {
      users = users.filter(user => user.age >= min)
    }
  }

  // 性别筛选
  if (filters.value.gender) {
    users = users.filter(user => user.gender === filters.value.gender)
  }

  return users
})

const totalPages = computed(() => Math.ceil(filteredUsers.value.length / pageSize))

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredUsers.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

const mostPopularMBTI = computed(() => {
  if (allUsers.value.length === 0) return 'N/A'
  
  const counts = {}
  allUsers.value.forEach(user => {
    counts[user.mbtiType] = (counts[user.mbtiType] || 0) + 1
  })
  
  return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b)
})

// 方法
const applyFilters = () => {
  currentPage.value = 1
}

const clearFilters = () => {
  filters.value = {
    mbtiType: '',
    ageRange: '',
    gender: ''
  }
  searchQuery.value = ''
  currentPage.value = 1
}

const getCompatibilityScore = (user) => {
  if (!currentUser.value) return 0
  
  const matches = LocalStorageManager.getMatches(currentUser.value, 100)
  const match = matches.find(m => m.id === user.id)
  return match ? match.matchScore : 0
}

// 组件挂载时初始化
onMounted(() => {
  currentUser.value = LocalStorageManager.getCurrentUser()
  allUsers.value = LocalStorageManager.getAllUsers()
})
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
