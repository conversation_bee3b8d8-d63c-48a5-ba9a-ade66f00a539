<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-100 py-8">
    <div class="container mx-auto px-4">
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
      </div>

      <!-- 注册表单 -->
      <div class="max-w-2xl mx-auto">
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-2xl font-bold text-center mb-6">
              <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                创建你的 MBTI 档案
              </span>
            </h2>

            <form @submit.prevent="handleSubmit" class="space-y-6">
              <!-- 基本信息 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">姓名 *</span>
                  </label>
                  <input 
                    type="text" 
                    v-model="formData.name"
                    placeholder="请输入你的姓名" 
                    class="input input-bordered w-full"
                    :class="{ 'input-error': errors.name }"
                    required
                  />
                  <label v-if="errors.name" class="label">
                    <span class="label-text-alt text-error">{{ errors.name }}</span>
                  </label>
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text font-medium">年龄 *</span>
                  </label>
                  <input 
                    type="number" 
                    v-model.number="formData.age"
                    placeholder="请输入年龄" 
                    class="input input-bordered w-full"
                    :class="{ 'input-error': errors.age }"
                    min="16"
                    max="100"
                    required
                  />
                  <label v-if="errors.age" class="label">
                    <span class="label-text-alt text-error">{{ errors.age }}</span>
                  </label>
                </div>
              </div>

              <!-- 性别选择 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">性别 *</span>
                </label>
                <div class="flex gap-4">
                  <label class="label cursor-pointer">
                    <input 
                      type="radio" 
                      name="gender" 
                      value="男"
                      v-model="formData.gender"
                      class="radio radio-primary" 
                    />
                    <span class="label-text ml-2">男</span>
                  </label>
                  <label class="label cursor-pointer">
                    <input 
                      type="radio" 
                      name="gender" 
                      value="女"
                      v-model="formData.gender"
                      class="radio radio-primary" 
                    />
                    <span class="label-text ml-2">女</span>
                  </label>
                  <label class="label cursor-pointer">
                    <input 
                      type="radio" 
                      name="gender" 
                      value="其他"
                      v-model="formData.gender"
                      class="radio radio-primary" 
                    />
                    <span class="label-text ml-2">其他</span>
                  </label>
                </div>
                <label v-if="errors.gender" class="label">
                  <span class="label-text-alt text-error">{{ errors.gender }}</span>
                </label>
              </div>

              <!-- MBTI 类型选择 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">MBTI 类型 *</span>
                </label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div 
                    v-for="mbti in MBTI_TYPES" 
                    :key="mbti.type"
                    class="relative"
                  >
                    <input 
                      type="radio" 
                      :id="mbti.type"
                      name="mbtiType" 
                      :value="mbti.type"
                      v-model="formData.mbtiType"
                      class="sr-only"
                    />
                    <label 
                      :for="mbti.type"
                      class="block p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:scale-105"
                      :class="[
                        formData.mbtiType === mbti.type 
                          ? 'border-purple-500 bg-purple-50 shadow-md' 
                          : 'border-gray-200 bg-white hover:border-purple-300',
                        mbti.color
                      ]"
                    >
                      <div class="text-center">
                        <div class="font-bold text-white text-sm mb-1">{{ mbti.type }}</div>
                        <div class="text-xs text-white opacity-90">{{ mbti.name }}</div>
                      </div>
                    </label>
                  </div>
                </div>
                <label v-if="errors.mbtiType" class="label">
                  <span class="label-text-alt text-error">{{ errors.mbtiType }}</span>
                </label>
              </div>

              <!-- 兴趣爱好 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">兴趣爱好 *</span>
                  <span class="label-text-alt">至少选择 3 个</span>
                </label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                  <label 
                    v-for="interest in COMMON_INTERESTS" 
                    :key="interest"
                    class="label cursor-pointer justify-start p-2 rounded-lg hover:bg-purple-50 transition-colors"
                  >
                    <input 
                      type="checkbox" 
                      :value="interest"
                      v-model="formData.interests"
                      class="checkbox checkbox-primary checkbox-sm" 
                    />
                    <span class="label-text ml-2 text-sm">{{ interest }}</span>
                  </label>
                </div>
                <label v-if="errors.interests" class="label">
                  <span class="label-text-alt text-error">{{ errors.interests }}</span>
                </label>
              </div>

              <!-- 自我介绍 -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">自我介绍</span>
                  <span class="label-text-alt">{{ formData.bio.length }}/200</span>
                </label>
                <textarea 
                  v-model="formData.bio"
                  class="textarea textarea-bordered h-24"
                  placeholder="简单介绍一下自己吧..."
                  maxlength="200"
                ></textarea>
              </div>

              <!-- 提交按钮 -->
              <div class="form-control mt-8">
                <button 
                  type="submit"
                  class="btn btn-primary btn-lg bg-gradient-to-r from-purple-600 to-pink-600 border-none text-white hover:from-purple-700 hover:to-pink-700"
                  :disabled="isSubmitting"
                >
                  <span v-if="isSubmitting" class="loading loading-spinner loading-sm"></span>
                  {{ isSubmitting ? '创建中...' : '🚀 创建档案并开始匹配' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { MBTI_TYPES, COMMON_INTERESTS } from '../utils/mbtiTypes.js'
import LocalStorageManager from '../utils/localStorage.js'

const router = useRouter()

// 表单数据
const formData = reactive({
  name: '',
  age: null,
  gender: '',
  mbtiType: '',
  interests: [],
  bio: ''
})

// 错误信息
const errors = ref({})
const isSubmitting = ref(false)

// 表单验证
const validateForm = () => {
  const newErrors = {}

  if (!formData.name.trim()) {
    newErrors.name = '请输入姓名'
  } else if (formData.name.trim().length < 2) {
    newErrors.name = '姓名至少需要2个字符'
  }

  if (!formData.age) {
    newErrors.age = '请输入年龄'
  } else if (formData.age < 16 || formData.age > 100) {
    newErrors.age = '年龄必须在16-100之间'
  }

  if (!formData.gender) {
    newErrors.gender = '请选择性别'
  }

  if (!formData.mbtiType) {
    newErrors.mbtiType = '请选择MBTI类型'
  }

  if (formData.interests.length < 3) {
    newErrors.interests = '请至少选择3个兴趣爱好'
  }

  errors.value = newErrors
  return Object.keys(newErrors).length === 0
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true

  try {
    // 添加用户到本地存储
    const result = LocalStorageManager.addUser({
      name: formData.name.trim(),
      age: formData.age,
      gender: formData.gender,
      mbtiType: formData.mbtiType,
      interests: formData.interests,
      bio: formData.bio.trim(),
      avatar: `https://images.unsplash.com/photo-${Math.floor(Math.random() * 1000000000)}?w=150&h=150&fit=crop&crop=face`
    })

    if (result.success) {
      // 设置为当前用户
      LocalStorageManager.setCurrentUser(result.data)
      
      // 跳转到匹配页面
      router.push('/matches')
    } else {
      alert('创建档案失败：' + result.message)
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    alert('创建档案失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}
</script>
