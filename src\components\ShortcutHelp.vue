<template>
  <!-- 快捷键帮助按钮 -->
  <button 
    class="btn btn-ghost btn-sm"
    @click="showHelp = true"
    title="快捷键帮助 (按 ? 键)"
  >
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <span class="hidden sm:inline ml-1">帮助</span>
  </button>

  <!-- 快捷键帮助模态框 -->
  <div v-if="showHelp" class="modal modal-open">
    <div class="modal-box max-w-2xl">
      <div class="flex justify-between items-center mb-6">
        <h3 class="font-bold text-xl">⌨️ 快捷键帮助</h3>
        <button 
          class="btn btn-ghost btn-sm btn-circle"
          @click="showHelp = false"
        >
          ✕
        </button>
      </div>

      <!-- 快捷键分类 -->
      <div class="space-y-6">
        <!-- 导航快捷键 -->
        <div>
          <h4 class="font-semibold text-lg mb-3 text-primary">🧭 导航快捷键</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div 
              v-for="shortcut in navigationShortcuts" 
              :key="shortcut.key"
              class="flex items-center justify-between p-3 bg-base-200 rounded-lg"
            >
              <span class="text-sm">{{ shortcut.description }}</span>
              <kbd class="kbd kbd-sm">{{ shortcut.key }}</kbd>
            </div>
          </div>
        </div>

        <!-- 搜索快捷键 -->
        <div>
          <h4 class="font-semibold text-lg mb-3 text-secondary">🔍 搜索快捷键</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div 
              v-for="shortcut in searchShortcuts" 
              :key="shortcut.key"
              class="flex items-center justify-between p-3 bg-base-200 rounded-lg"
            >
              <span class="text-sm">{{ shortcut.description }}</span>
              <kbd class="kbd kbd-sm">{{ shortcut.key }}</kbd>
            </div>
          </div>
        </div>

        <!-- 界面快捷键 -->
        <div>
          <h4 class="font-semibold text-lg mb-3 text-accent">🎨 界面快捷键</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div 
              v-for="shortcut in interfaceShortcuts" 
              :key="shortcut.key"
              class="flex items-center justify-between p-3 bg-base-200 rounded-lg"
            >
              <span class="text-sm">{{ shortcut.description }}</span>
              <kbd class="kbd kbd-sm">{{ shortcut.key }}</kbd>
            </div>
          </div>
        </div>

        <!-- 通用快捷键 -->
        <div>
          <h4 class="font-semibold text-lg mb-3 text-info">⚡ 通用快捷键</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div 
              v-for="shortcut in generalShortcuts" 
              :key="shortcut.key"
              class="flex items-center justify-between p-3 bg-base-200 rounded-lg"
            >
              <span class="text-sm">{{ shortcut.description }}</span>
              <kbd class="kbd kbd-sm">{{ shortcut.key }}</kbd>
            </div>
          </div>
        </div>
      </div>

      <!-- 提示信息 -->
      <div class="alert alert-info mt-6">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
          <h3 class="font-bold">💡 小贴士</h3>
          <div class="text-sm">
            <p>• 在 Mac 上，请使用 Cmd 键替代 Ctrl 键</p>
            <p>• 按 <kbd class="kbd kbd-xs">?</kbd> 键可以快速打开此帮助</p>
            <p>• 大部分快捷键在任何页面都可以使用</p>
          </div>
        </div>
      </div>

      <!-- 无障碍信息 -->
      <div class="alert alert-success mt-4">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
          <h3 class="font-bold">♿ 无障碍支持</h3>
          <div class="text-sm">
            <p>• 支持键盘导航和屏幕阅读器</p>
            <p>• 按 <kbd class="kbd kbd-xs">Tab</kbd> 键在元素间切换</p>
            <p>• 按 <kbd class="kbd kbd-xs">Enter</kbd> 或 <kbd class="kbd kbd-xs">Space</kbd> 激活按钮</p>
          </div>
        </div>
      </div>

      <div class="modal-action">
        <button 
          class="btn btn-primary"
          @click="showHelp = false"
        >
          知道了
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const showHelp = ref(false)

// 快捷键分类
const navigationShortcuts = [
  { key: 'Ctrl+H', description: '返回首页' },
  { key: 'Ctrl+P', description: '个人资料' },
  { key: 'Ctrl+M', description: '消息中心' },
  { key: 'Ctrl+U', description: '用户列表' },
  { key: 'Ctrl+S', description: '数据统计' },
  { key: 'Ctrl+F', description: '关注粉丝' }
]

const searchShortcuts = [
  { key: 'Ctrl+K', description: '快速搜索' },
  { key: '↑ ↓', description: '选择搜索建议' },
  { key: 'Enter', description: '确认搜索' },
  { key: 'Esc', description: '关闭搜索建议' }
]

const interfaceShortcuts = [
  { key: 'Ctrl+T', description: '切换主题' },
  { key: '?', description: '显示帮助' },
  { key: 'Ctrl+,', description: '打开设置' },
  { key: 'F11', description: '全屏模式' }
]

const generalShortcuts = [
  { key: 'Tab', description: '下一个元素' },
  { key: 'Shift+Tab', description: '上一个元素' },
  { key: 'Enter', description: '确认/激活' },
  { key: 'Space', description: '选择/激活' },
  { key: 'Esc', description: '取消/关闭' },
  { key: 'Ctrl+R', description: '刷新页面' }
]

// 全局快捷键处理
const handleGlobalKeydown = (event) => {
  // 显示帮助
  if (event.key === '?' && !event.ctrlKey && !event.metaKey && !event.altKey) {
    // 确保不在输入框中
    const activeElement = document.activeElement
    if (activeElement && (
      activeElement.tagName === 'INPUT' || 
      activeElement.tagName === 'TEXTAREA' || 
      activeElement.contentEditable === 'true'
    )) {
      return
    }
    
    event.preventDefault()
    showHelp.value = true
  }
  
  // 关闭帮助
  if (event.key === 'Escape' && showHelp.value) {
    event.preventDefault()
    showHelp.value = false
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
})
</script>

<style scoped>
/* 键盘按键样式增强 */
.kbd {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
}

/* 模态框动画 */
.modal-open .modal-box {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 快捷键项目悬停效果 */
.bg-base-200:hover {
  background-color: hsl(var(--b3));
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 响应式网格调整 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
