<template>
  <div class="min-h-screen bg-theme-gradient py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              👥 关注与粉丝
            </span>
          </h1>
        </div>
        
        <button 
          class="btn btn-outline btn-sm"
          @click="$router.push('/profile')"
        >
          我的资料
        </button>
      </div>

      <!-- 标签页 -->
      <div class="tabs tabs-boxed justify-center mb-8 bg-white/80 backdrop-blur-sm">
        <a 
          class="tab"
          :class="{ 'tab-active': activeTab === 'following' }"
          @click="activeTab = 'following'"
        >
          我的关注 ({{ followingList.length }})
        </a>
        <a 
          class="tab"
          :class="{ 'tab-active': activeTab === 'followers' }"
          @click="activeTab = 'followers'"
        >
          我的粉丝 ({{ followersList.length }})
        </a>
        <a 
          class="tab"
          :class="{ 'tab-active': activeTab === 'mutual' }"
          @click="activeTab = 'mutual'"
        >
          互相关注 ({{ mutualFollows.length }})
        </a>
      </div>

      <!-- 搜索和筛选 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-lg mb-8">
        <div class="card-body">
          <div class="flex flex-wrap gap-4 items-center">
            <div class="form-control flex-1">
              <input 
                type="text"
                v-model="searchQuery"
                placeholder="搜索用户..."
                class="input input-bordered input-sm"
              />
            </div>
            <div class="form-control">
              <select 
                v-model="sortBy"
                class="select select-bordered select-sm"
              >
                <option value="recent">最近关注</option>
                <option value="name">按姓名</option>
                <option value="compatibility">按匹配度</option>
                <option value="activity">按活跃度</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div 
          v-for="user in filteredUsers" 
          :key="user.id"
          class="card bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <div class="card-body">
            <!-- 用户信息 -->
            <div class="flex items-center space-x-4 mb-4">
              <div class="avatar">
                <div class="w-16 h-16 rounded-full ring ring-purple-200 ring-offset-2 relative">
                  <img :src="user.avatar" :alt="user.name" />
                  <div 
                    v-if="user.isOnline"
                    class="absolute bottom-0 right-0 w-4 h-4 bg-green-500 rounded-full border-2 border-white"
                  ></div>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-bold text-gray-800">{{ user.name }}</h3>
                <div class="flex items-center space-x-2 mt-1">
                  <span 
                    class="badge text-white text-xs px-2 py-1"
                    :class="getMBTIColor(user.mbtiType)"
                  >
                    {{ user.mbtiType }}
                  </span>
                  <span class="text-sm text-gray-600">{{ user.age }}岁</span>
                </div>
              </div>
            </div>

            <!-- 关注状态标识 -->
            <div class="flex flex-wrap gap-2 mb-4">
              <span 
                v-if="activeTab === 'following' || isMutualFollow(user.id)"
                class="badge badge-primary text-white"
              >
                ✓ 已关注
              </span>
              <span 
                v-if="activeTab === 'followers' || isMutualFollow(user.id)"
                class="badge badge-secondary text-white"
              >
                👥 关注我
              </span>
              <span 
                v-if="isMutualFollow(user.id)"
                class="badge badge-accent text-white"
              >
                💕 互相关注
              </span>
            </div>

            <!-- 个人标签 -->
            <div v-if="user.tags && user.tags.length > 0" class="mb-4">
              <div class="flex flex-wrap gap-1">
                <span 
                  v-for="tag in user.tags.slice(0, 3)" 
                  :key="tag"
                  class="badge badge-outline text-xs"
                >
                  {{ tag }}
                </span>
              </div>
            </div>

            <!-- 自我介绍 -->
            <p v-if="user.bio" class="text-gray-600 text-sm mb-4 line-clamp-2">
              {{ user.bio }}
            </p>

            <!-- 匹配度 -->
            <div class="mb-4">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">匹配度：</span>
                <div 
                  class="radial-progress text-primary text-sm"
                  :style="`--value:${getCompatibilityScore(user)}; --size:2rem; --thickness: 3px;`"
                >
                  {{ getCompatibilityScore(user) }}%
                </div>
              </div>
            </div>

            <!-- 关注时间 -->
            <div class="text-xs text-gray-500 mb-4">
              <span v-if="activeTab === 'following'">
                关注时间：{{ getFollowTime(user.id, 'following') }}
              </span>
              <span v-else-if="activeTab === 'followers'">
                关注我：{{ getFollowTime(user.id, 'followers') }}
              </span>
              <span v-else>
                互相关注
              </span>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions justify-end">
              <button 
                class="btn btn-outline btn-sm"
                @click="sendMessage(user)"
              >
                💬 发消息
              </button>
              <button 
                v-if="activeTab === 'following' || isMutualFollow(user.id)"
                class="btn btn-ghost btn-sm"
                @click="unfollowUser(user.id)"
              >
                ✗ 取消关注
              </button>
              <button 
                v-if="activeTab === 'followers' && !isFollowing(user.id)"
                class="btn btn-primary btn-sm"
                @click="followUser(user.id)"
              >
                ⭐ 回关
              </button>
              <button 
                class="btn btn-outline btn-sm"
                @click="viewProfile(user)"
              >
                👤 查看资料
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredUsers.length === 0" class="text-center py-12">
        <div class="text-6xl mb-4">
          {{ activeTab === 'following' ? '👥' : activeTab === 'followers' ? '🫂' : '💕' }}
        </div>
        <h3 class="text-2xl font-bold text-gray-600 mb-4">
          {{ getEmptyStateTitle() }}
        </h3>
        <p class="text-gray-500 mb-6">{{ getEmptyStateDescription() }}</p>
        <button 
          class="btn btn-primary"
          @click="$router.push('/users')"
        >
          去发现更多用户
        </button>
      </div>

      <!-- 统计信息 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
          <div class="card-body text-center">
            <div class="text-3xl text-purple-600 mb-2">{{ followingList.length }}</div>
            <div class="text-sm text-gray-600">我的关注</div>
          </div>
        </div>
        
        <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
          <div class="card-body text-center">
            <div class="text-3xl text-pink-600 mb-2">{{ followersList.length }}</div>
            <div class="text-sm text-gray-600">我的粉丝</div>
          </div>
        </div>
        
        <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
          <div class="card-body text-center">
            <div class="text-3xl text-indigo-600 mb-2">{{ mutualFollows.length }}</div>
            <div class="text-sm text-gray-600">互相关注</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import LocalStorageManager from '../utils/localStorage.js'
import { getMBTIColor } from '../utils/mbtiTypes.js'

const router = useRouter()

// 响应式数据
const currentUser = ref(null)
const activeTab = ref('following')
const searchQuery = ref('')
const sortBy = ref('recent')
const followingList = ref([])
const followersList = ref([])

// 计算属性
const mutualFollows = computed(() => {
  const followingIds = followingList.value.map(user => user.id)
  const followerIds = followersList.value.map(user => user.id)
  
  return followingList.value.filter(user => followerIds.includes(user.id))
})

const currentList = computed(() => {
  switch (activeTab.value) {
    case 'following':
      return followingList.value
    case 'followers':
      return followersList.value
    case 'mutual':
      return mutualFollows.value
    default:
      return []
  }
})

const filteredUsers = computed(() => {
  let users = currentList.value

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    users = users.filter(user => 
      user.name.toLowerCase().includes(query) ||
      (user.tags && user.tags.some(tag => tag.toLowerCase().includes(query))) ||
      (user.bio && user.bio.toLowerCase().includes(query))
    )
  }

  // 排序
  users.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'compatibility':
        return getCompatibilityScore(b) - getCompatibilityScore(a)
      case 'activity':
        return new Date(b.lastActive || b.createdAt) - new Date(a.lastActive || a.createdAt)
      case 'recent':
      default:
        return 0 // 保持原有顺序
    }
  })

  return users
})

// 方法
const getCompatibilityScore = (user) => {
  if (!currentUser.value) return 0
  
  const matches = LocalStorageManager.getMatches(currentUser.value, 100)
  const match = matches.find(m => m.id === user.id)
  return match ? match.matchScore : 0
}

const isMutualFollow = (userId) => {
  return mutualFollows.value.some(user => user.id === userId)
}

const isFollowing = (userId) => {
  return LocalStorageManager.isFollowing(currentUser.value.id, userId)
}

const followUser = (userId) => {
  LocalStorageManager.followUser(currentUser.value.id, userId)
  loadFollowData()
}

const unfollowUser = (userId) => {
  if (confirm('确定要取消关注吗？')) {
    LocalStorageManager.unfollowUser(currentUser.value.id, userId)
    loadFollowData()
  }
}

const getFollowTime = (userId, type) => {
  // 这里可以实现获取关注时间的逻辑
  // 目前返回模拟数据
  return '最近'
}

const sendMessage = (user) => {
  console.log('发送消息给:', user.name)
  router.push('/messages')
}

const viewProfile = (user) => {
  console.log('查看用户资料:', user.name)
  // 记录访客
  LocalStorageManager.recordVisitor(user.id, currentUser.value.id)
}

const getEmptyStateTitle = () => {
  switch (activeTab.value) {
    case 'following':
      return '还没有关注任何人'
    case 'followers':
      return '还没有粉丝'
    case 'mutual':
      return '还没有互相关注的朋友'
    default:
      return '暂无数据'
  }
}

const getEmptyStateDescription = () => {
  switch (activeTab.value) {
    case 'following':
      return '去用户列表发现有趣的人并关注他们吧'
    case 'followers':
      return '完善你的资料，让更多人发现并关注你'
    case 'mutual':
      return '与你关注的人互动，建立更深的连接'
    default:
      return ''
  }
}

const loadFollowData = () => {
  if (!currentUser.value) return

  // 获取关注列表
  const followingIds = LocalStorageManager.getFollowing(currentUser.value.id)
  followingList.value = followingIds.map(id => LocalStorageManager.getUserById(id)).filter(Boolean)

  // 获取粉丝列表
  const followerIds = LocalStorageManager.getFollowers(currentUser.value.id)
  followersList.value = followerIds.map(id => LocalStorageManager.getUserById(id)).filter(Boolean)
}

// 组件挂载时初始化
onMounted(() => {
  currentUser.value = LocalStorageManager.getCurrentUser()
  
  if (!currentUser.value) {
    router.push('/register')
    return
  }
  
  loadFollowData()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
