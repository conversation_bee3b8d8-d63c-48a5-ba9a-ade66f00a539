const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const db = app.database();

// MBTI 类型兼容性矩阵
const MBTI_COMPATIBILITY = {
  'ENFP': { 'INTJ': 95, 'INFJ': 90, 'ENFJ': 85, 'ENTP': 80, 'INFP': 85, 'ISFP': 70, 'ISTP': 65, 'ESFP': 75, 'ESTP': 70, 'ENTJ': 75, 'ESTJ': 60, 'ISFJ': 70, 'ISTJ': 55, 'ESFJ': 65, 'INTP': 80, 'ENFP': 85 },
  'INTJ': { 'ENFP': 95, 'ENTP': 90, 'INFP': 85, 'ENFJ': 80, 'INFJ': 85, 'ISFP': 70, 'ISTP': 75, 'ESFP': 60, 'ESTP': 65, 'ENTJ': 80, 'ESTJ': 75, 'ISFJ': 65, 'ISTJ': 70, 'ESFJ': 60, 'INTP': 85, 'INTJ': 80 },
  'INFJ': { 'ENFP': 90, 'ENTP': 85, 'INFP': 90, 'ENFJ': 85, 'INTJ': 85, 'ISFP': 80, 'ISTP': 70, 'ESFP': 65, 'ESTP': 60, 'ENTJ': 75, 'ESTJ': 65, 'ISFJ': 80, 'ISTJ': 75, 'ESFJ': 70, 'INTP': 80, 'INFJ': 85 },
  'ENFJ': { 'INFP': 90, 'ISFP': 85, 'ENFP': 85, 'INFJ': 85, 'INTJ': 80, 'ENTP': 80, 'ISTP': 70, 'ESFP': 80, 'ESTP': 75, 'ENTJ': 80, 'ESTJ': 75, 'ISFJ': 85, 'ISTJ': 70, 'ESFJ': 80, 'INTP': 75, 'ENFJ': 80 },
  'ENTP': { 'INTJ': 90, 'INFJ': 85, 'ENFJ': 80, 'ENFP': 80, 'INFP': 80, 'ISFP': 70, 'ISTP': 75, 'ESFP': 75, 'ESTP': 80, 'ENTJ': 85, 'ESTJ': 75, 'ISFJ': 65, 'ISTJ': 60, 'ESFJ': 70, 'INTP': 85, 'ENTP': 80 },
  'INFP': { 'ENFJ': 90, 'INFJ': 90, 'ENFP': 85, 'INTJ': 85, 'ENTP': 80, 'ISFP': 85, 'ISTP': 75, 'ESFP': 70, 'ESTP': 65, 'ENTJ': 70, 'ESTJ': 60, 'ISFJ': 80, 'ISTJ': 70, 'ESFJ': 75, 'INTP': 80, 'INFP': 85 },
  'ISFP': { 'ENFJ': 85, 'ESFJ': 80, 'ISFJ': 85, 'INFP': 85, 'ENFP': 70, 'INFJ': 80, 'INTJ': 70, 'ENTP': 70, 'ISTP': 80, 'ESFP': 85, 'ESTP': 80, 'ENTJ': 65, 'ESTJ': 70, 'ISTJ': 75, 'INTP': 70, 'ISFP': 80 },
  'ISTP': { 'ESFJ': 75, 'ESTJ': 80, 'ISFJ': 70, 'ISTJ': 80, 'ENFP': 65, 'INFJ': 70, 'INTJ': 75, 'ENTP': 75, 'ISFP': 80, 'ESFP': 85, 'ESTP': 90, 'ENTJ': 75, 'ENFJ': 70, 'INFP': 75, 'INTP': 80, 'ISTP': 85 },
  'ESFP': { 'ISFJ': 85, 'ESFJ': 85, 'ISTJ': 75, 'ESTJ': 80, 'ENFP': 75, 'INFJ': 65, 'INTJ': 60, 'ENTP': 75, 'ISFP': 85, 'ISTP': 85, 'ESTP': 90, 'ENTJ': 70, 'ENFJ': 80, 'INFP': 70, 'INTP': 70, 'ESFP': 85 },
  'ESTP': { 'ISFJ': 80, 'ESFJ': 80, 'ISTJ': 80, 'ESTJ': 85, 'ENFP': 70, 'INFJ': 60, 'INTJ': 65, 'ENTP': 80, 'ISFP': 80, 'ISTP': 90, 'ESFP': 90, 'ENTJ': 80, 'ENFJ': 75, 'INFP': 65, 'INTP': 75, 'ESTP': 85 },
  'ENTJ': { 'INTP': 90, 'ENTP': 85, 'INTJ': 80, 'ENFP': 75, 'INFJ': 75, 'ENFJ': 80, 'INFP': 70, 'ISFP': 65, 'ISTP': 75, 'ESFP': 70, 'ESTP': 80, 'ESTJ': 85, 'ISFJ': 70, 'ISTJ': 80, 'ESFJ': 75, 'ENTJ': 80 },
  'ESTJ': { 'ISFJ': 85, 'ISTJ': 90, 'ESFJ': 85, 'ISTP': 80, 'ESFP': 80, 'ESTP': 85, 'ENTJ': 85, 'ENFP': 60, 'INFJ': 65, 'INTJ': 75, 'ENTP': 75, 'ENFJ': 75, 'INFP': 60, 'ISFP': 70, 'INTP': 75, 'ESTJ': 85 },
  'ISFJ': { 'ESFP': 85, 'ESTP': 80, 'ESTJ': 85, 'ISFP': 85, 'ESFJ': 90, 'ENFJ': 85, 'INFP': 80, 'INFJ': 80, 'ENFP': 70, 'INTJ': 65, 'ENTP': 65, 'ISTP': 70, 'ENTJ': 70, 'ISTJ': 85, 'INTP': 70, 'ISFJ': 85 },
  'ISTJ': { 'ESFP': 75, 'ESTP': 80, 'ESTJ': 90, 'ISFP': 75, 'ESFJ': 85, 'ENFJ': 70, 'INFP': 70, 'INFJ': 75, 'ENFP': 55, 'INTJ': 70, 'ENTP': 60, 'ISTP': 80, 'ENTJ': 80, 'ISFJ': 85, 'INTP': 75, 'ISTJ': 85 },
  'ESFJ': { 'ISFP': 80, 'ISTP': 75, 'ESTJ': 85, 'ESFP': 85, 'ISFJ': 90, 'ENFJ': 80, 'INFP': 75, 'INFJ': 70, 'ENFP': 65, 'INTJ': 60, 'ENTP': 70, 'ESTP': 80, 'ENTJ': 75, 'ISTJ': 85, 'INTP': 65, 'ESFJ': 85 },
  'INTP': { 'ENTJ': 90, 'ENTP': 85, 'INTJ': 85, 'ENFP': 80, 'INFJ': 80, 'ENFJ': 75, 'INFP': 80, 'ISFP': 70, 'ISTP': 80, 'ESFP': 70, 'ESTP': 75, 'ESTJ': 75, 'ISFJ': 70, 'ISTJ': 75, 'ESFJ': 65, 'INTP': 85 }
};

/**
 * 计算两个用户的匹配度
 * @param {Object} user1 用户1
 * @param {Object} user2 用户2
 * @returns {Object} 匹配结果
 */
function calculateMatch(user1, user2) {
  // MBTI 兼容性得分 (40%)
  const mbtiScore = MBTI_COMPATIBILITY[user1.mbtiType]?.[user2.mbtiType] || 50;
  
  // 兴趣爱好相似度 (30%)
  const commonInterests = user1.interests.filter(interest => 
    user2.interests.includes(interest)
  );
  const interestScore = commonInterests.length > 0 
    ? (commonInterests.length / Math.max(user1.interests.length, user2.interests.length)) * 100
    : 0;
  
  // 年龄差异 (20%)
  const ageDiff = Math.abs(user1.age - user2.age);
  const ageScore = Math.max(0, 100 - ageDiff * 5);
  
  // 个人标签相似度 (10%)
  const user1Tags = user1.tags || [];
  const user2Tags = user2.tags || [];
  const commonTags = user1Tags.filter(tag => user2Tags.includes(tag));
  const tagScore = commonTags.length > 0 
    ? (commonTags.length / Math.max(user1Tags.length, user2Tags.length)) * 100
    : 0;
  
  // 加权计算总分
  const totalScore = Math.round(
    mbtiScore * 0.4 + 
    interestScore * 0.3 + 
    ageScore * 0.2 + 
    tagScore * 0.1
  );
  
  // 生成匹配原因
  const reasons = [];
  if (mbtiScore >= 80) {
    reasons.push(`MBTI 类型 ${user1.mbtiType} 和 ${user2.mbtiType} 高度兼容`);
  }
  if (commonInterests.length > 0) {
    reasons.push(`共同兴趣：${commonInterests.slice(0, 3).join('、')}`);
  }
  if (ageDiff <= 3) {
    reasons.push('年龄相近，容易产生共鸣');
  }
  if (commonTags.length > 0) {
    reasons.push(`相似标签：${commonTags.slice(0, 2).join('、')}`);
  }
  
  return {
    matchScore: totalScore,
    reasons: reasons.length > 0 ? reasons : ['基础兼容性匹配'],
    commonInterests,
    commonTags,
    mbtiCompatibility: mbtiScore
  };
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  try {
    const { action, userId, targetUserId, limit = 10 } = event;
    
    switch (action) {
      case 'getMatches':
        // 获取用户的匹配列表
        const userResult = await db.collection('users').doc(userId).get();
        if (!userResult.data.length) {
          return { success: false, message: '用户不存在' };
        }
        
        const currentUser = userResult.data[0];
        
        // 获取所有其他用户
        const allUsersResult = await db.collection('users')
          .where({
            _id: db.command.neq(userId)
          })
          .get();
        
        // 计算匹配度并排序
        const matches = allUsersResult.data
          .map(user => ({
            ...user,
            ...calculateMatch(currentUser, user)
          }))
          .sort((a, b) => b.matchScore - a.matchScore)
          .slice(0, limit);
        
        return {
          success: true,
          data: matches
        };
        
      case 'calculateMatch':
        // 计算两个特定用户的匹配度
        const user1Result = await db.collection('users').doc(userId).get();
        const user2Result = await db.collection('users').doc(targetUserId).get();
        
        if (!user1Result.data.length || !user2Result.data.length) {
          return { success: false, message: '用户不存在' };
        }
        
        const matchResult = calculateMatch(user1Result.data[0], user2Result.data[0]);
        
        return {
          success: true,
          data: matchResult
        };
        
      case 'updateMatchHistory':
        // 更新匹配历史记录
        await db.collection('match_history').add({
          userId,
          targetUserId,
          matchScore: event.matchScore,
          action: event.matchAction, // 'like', 'pass', 'super_like'
          timestamp: new Date()
        });
        
        return {
          success: true,
          message: '匹配历史已更新'
        };
        
      default:
        return {
          success: false,
          message: '不支持的操作'
        };
    }
    
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: error.message
    };
  }
};
