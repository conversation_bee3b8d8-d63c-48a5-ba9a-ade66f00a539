<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-100">
    <!-- 导航栏 -->
    <nav class="navbar bg-white/80 backdrop-blur-sm shadow-sm">
      <div class="navbar-start">
        <div class="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
          💝 MBTI - Friends
        </div>
      </div>
      <div class="navbar-end">
        <div class="flex items-center space-x-2">
          <!-- 主题切换器 -->
          <ThemeSwitcher />

          <!-- 快捷键帮助 -->
          <ShortcutHelp />

          <!-- 用户菜单 -->
          <div v-if="currentUser" class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
              {{ currentUser.name }} ▼
            </div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
              <li><a @click="$router.push('/profile')">👤 我的资料</a></li>
              <li><a @click="$router.push('/messages')">💬 消息中心</a></li>
              <li><a @click="$router.push('/follows')">👥 关注粉丝</a></li>
              <li><a @click="$router.push('/stats')">📊 数据统计</a></li>
              <li><hr></li>
              <li><a @click="logout">🚪 退出登录</a></li>
            </ul>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="hero min-h-[calc(100vh-4rem)]">
      <div class="hero-content text-center max-w-4xl">
        <div class="w-full">
          <!-- 标题区域 -->
          <div class="mb-12">
            <h1 class="text-6xl font-bold mb-6">
              <span class="bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 bg-clip-text text-transparent">
                MBTI - Friends
              </span>
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              基于 MBTI 人格类型的智能匹配平台，找到与你最契合的朋友 ✨
            </p>

            <!-- 特色功能 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div class="card bg-white/70 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
                <div class="card-body items-center text-center">
                  <div class="text-4xl mb-4">🧠</div>
                  <h3 class="card-title text-lg">智能匹配</h3>
                  <p class="text-sm text-gray-600">基于 MBTI 类型和兴趣爱好的科学匹配算法</p>
                </div>
              </div>

              <div class="card bg-white/70 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
                <div class="card-body items-center text-center">
                  <div class="text-4xl mb-4">💫</div>
                  <h3 class="card-title text-lg">个性化推荐</h3>
                  <p class="text-sm text-gray-600">为你推荐最合适的 3 位潜在朋友</p>
                </div>
              </div>

              <div class="card bg-white/70 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
                <div class="card-body items-center text-center">
                  <div class="text-4xl mb-4">🎨</div>
                  <h3 class="card-title text-lg">美观界面</h3>
                  <p class="text-sm text-gray-600">现代化设计，流畅的用户体验</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <button
              v-if="!currentUser"
              class="btn btn-primary btn-lg bg-gradient-to-r from-purple-600 to-pink-600 border-none text-white hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-200"
              @click="$router.push('/register')"
            >
              <span class="text-lg">🚀 开始匹配</span>
            </button>

            <button
              v-else
              class="btn btn-primary btn-lg bg-gradient-to-r from-purple-600 to-pink-600 border-none text-white hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-200"
              @click="$router.push('/matches')"
            >
              <span class="text-lg">💝 查看匹配</span>
            </button>

            <button
              class="btn btn-outline btn-lg border-purple-300 text-purple-600 hover:bg-purple-600 hover:border-purple-600 transform hover:scale-105 transition-all duration-200"
              @click="$router.push('/users')"
            >
              <span class="text-lg">👥 浏览用户</span>
            </button>
          </div>

          <!-- 统计信息 -->
          <div class="stats stats-vertical lg:stats-horizontal shadow-lg bg-white/70 backdrop-blur-sm">
            <div class="stat">
              <div class="stat-figure text-purple-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="stat-title">注册用户</div>
              <div class="stat-value text-purple-600">{{ userCount }}</div>
              <div class="stat-desc">活跃的 MBTI 爱好者</div>
            </div>

            <div class="stat">
              <div class="stat-figure text-pink-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                </svg>
              </div>
              <div class="stat-title">MBTI 类型</div>
              <div class="stat-value text-pink-600">16</div>
              <div class="stat-desc">种人格类型支持</div>
            </div>

            <div class="stat">
              <div class="stat-figure text-indigo-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              </div>
              <div class="stat-title">成功匹配</div>
              <div class="stat-value text-indigo-600">{{ matchCount }}</div>
              <div class="stat-desc">个性化推荐</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <Footer/>
  </div>
</template>

<script setup>
import Footer from '../components/Footer.vue'
import ThemeSwitcher from '../components/ThemeSwitcher.vue'
import ShortcutHelp from '../components/ShortcutHelp.vue'
import { ref, onMounted, computed } from 'vue'
import LocalStorageManager from '../utils/localStorage.js'
import { ThemeManager } from '../utils/theme.js'

// 响应式数据
const currentUser = ref(null)
const userCount = ref(0)

// 计算匹配次数（模拟数据）
const matchCount = computed(() => {
  return userCount.value > 0 ? userCount.value * 3 : 0
})

// 退出登录
const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    LocalStorageManager.clearCurrentUser()
    currentUser.value = null
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  // 初始化主题
  ThemeManager.init()

  // 初始化示例数据
  LocalStorageManager.initSampleData()

  // 获取当前用户
  currentUser.value = LocalStorageManager.getCurrentUser()

  // 获取用户总数
  const users = LocalStorageManager.getAllUsers()
  userCount.value = users.length
})
</script>