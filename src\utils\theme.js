// 主题管理工具

const STORAGE_KEY = 'mbti_friends_theme'

// 可用主题列表
export const THEMES = [
  {
    name: 'light',
    displayName: '浅色主题',
    icon: '☀️',
    colors: {
      primary: 'from-purple-600 to-pink-600',
      secondary: 'from-blue-500 to-indigo-600',
      accent: 'from-green-500 to-teal-600',
      background: 'from-purple-50 via-pink-50 to-indigo-100'
    }
  },
  {
    name: 'dark',
    displayName: '深色主题',
    icon: '🌙',
    colors: {
      primary: 'from-purple-400 to-pink-400',
      secondary: 'from-blue-400 to-indigo-400',
      accent: 'from-green-400 to-teal-400',
      background: 'from-gray-900 via-purple-900 to-indigo-900'
    }
  },
  {
    name: 'sunset',
    displayName: '日落主题',
    icon: '🌅',
    colors: {
      primary: 'from-orange-500 to-red-500',
      secondary: 'from-yellow-500 to-orange-500',
      accent: 'from-pink-500 to-red-500',
      background: 'from-orange-100 via-red-50 to-pink-100'
    }
  },
  {
    name: 'ocean',
    displayName: '海洋主题',
    icon: '🌊',
    colors: {
      primary: 'from-blue-600 to-cyan-600',
      secondary: 'from-teal-500 to-blue-500',
      accent: 'from-cyan-500 to-teal-500',
      background: 'from-blue-50 via-cyan-50 to-teal-100'
    }
  },
  {
    name: 'forest',
    displayName: '森林主题',
    icon: '🌲',
    colors: {
      primary: 'from-green-600 to-emerald-600',
      secondary: 'from-emerald-500 to-green-500',
      accent: 'from-lime-500 to-green-500',
      background: 'from-green-50 via-emerald-50 to-lime-100'
    }
  }
]

// 主题管理类
export class ThemeManager {
  /**
   * 获取当前主题
   * @returns {string} 主题名称
   */
  static getCurrentTheme() {
    try {
      return localStorage.getItem(STORAGE_KEY) || 'light'
    } catch (error) {
      return 'light'
    }
  }

  /**
   * 设置主题
   * @param {string} themeName - 主题名称
   */
  static setTheme(themeName) {
    try {
      if (THEMES.some(theme => theme.name === themeName)) {
        localStorage.setItem(STORAGE_KEY, themeName)
        this.applyTheme(themeName)
      }
    } catch (error) {
      console.error('设置主题失败:', error)
    }
  }

  /**
   * 应用主题到页面
   * @param {string} themeName - 主题名称
   */
  static applyTheme(themeName) {
    const theme = THEMES.find(t => t.name === themeName)
    if (!theme) return

    const root = document.documentElement

    // 设置 DaisyUI 主题
    let daisyTheme = 'light'
    if (themeName === 'dark') {
      daisyTheme = 'dark'
    } else if (themeName === 'sunset') {
      daisyTheme = 'sunset'
    } else if (themeName === 'ocean') {
      daisyTheme = 'ocean'
    } else if (themeName === 'forest') {
      daisyTheme = 'forest'
    }

    document.documentElement.setAttribute('data-theme', daisyTheme)

    // 设置自定义 CSS 变量
    root.style.setProperty('--theme-primary', theme.colors.primary)
    root.style.setProperty('--theme-secondary', theme.colors.secondary)
    root.style.setProperty('--theme-accent', theme.colors.accent)
    root.style.setProperty('--theme-background', theme.colors.background)

    // 更新页面背景
    const body = document.body
    body.className = body.className.replace(/theme-\w+/g, '')
    body.classList.add(`theme-${themeName}`)
  }

  /**
   * 获取主题信息
   * @param {string} themeName - 主题名称
   * @returns {Object|null} 主题信息
   */
  static getThemeInfo(themeName) {
    return THEMES.find(theme => theme.name === themeName) || null
  }

  /**
   * 获取所有主题
   * @returns {Array} 主题列表
   */
  static getAllThemes() {
    return THEMES
  }

  /**
   * 切换到下一个主题
   */
  static nextTheme() {
    const currentTheme = this.getCurrentTheme()
    const currentIndex = THEMES.findIndex(theme => theme.name === currentTheme)
    const nextIndex = (currentIndex + 1) % THEMES.length
    this.setTheme(THEMES[nextIndex].name)
  }

  /**
   * 初始化主题
   */
  static init() {
    const currentTheme = this.getCurrentTheme()
    this.applyTheme(currentTheme)
  }
}

// 搜索建议管理
export class SearchSuggestionManager {
  static STORAGE_KEY = 'mbti_friends_search_history'
  static MAX_HISTORY = 10

  /**
   * 获取搜索历史
   * @returns {Array} 搜索历史列表
   */
  static getSearchHistory() {
    try {
      const history = localStorage.getItem(this.STORAGE_KEY)
      return history ? JSON.parse(history) : []
    } catch (error) {
      return []
    }
  }

  /**
   * 添加搜索记录
   * @param {string} query - 搜索关键词
   */
  static addSearchHistory(query) {
    if (!query || query.trim().length < 2) return

    try {
      let history = this.getSearchHistory()
      const trimmedQuery = query.trim()
      
      // 移除重复项
      history = history.filter(item => item !== trimmedQuery)
      
      // 添加到开头
      history.unshift(trimmedQuery)
      
      // 限制数量
      if (history.length > this.MAX_HISTORY) {
        history = history.slice(0, this.MAX_HISTORY)
      }
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(history))
    } catch (error) {
      console.error('添加搜索历史失败:', error)
    }
  }

  /**
   * 清除搜索历史
   */
  static clearSearchHistory() {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
    } catch (error) {
      console.error('清除搜索历史失败:', error)
    }
  }

  /**
   * 获取搜索建议
   * @param {string} query - 搜索关键词
   * @param {Array} users - 用户列表
   * @returns {Array} 搜索建议列表
   */
  static getSearchSuggestions(query, users = []) {
    if (!query || query.length < 1) {
      return this.getSearchHistory().map(item => ({
        type: 'history',
        text: item,
        icon: '🕒'
      }))
    }

    const suggestions = []
    const lowerQuery = query.toLowerCase()

    // 用户名建议
    const userSuggestions = users
      .filter(user => user.name.toLowerCase().includes(lowerQuery))
      .slice(0, 5)
      .map(user => ({
        type: 'user',
        text: user.name,
        icon: '👤',
        user: user
      }))

    // 兴趣建议
    const interestSuggestions = []
    const interests = new Set()
    users.forEach(user => {
      user.interests.forEach(interest => {
        if (interest.toLowerCase().includes(lowerQuery)) {
          interests.add(interest)
        }
      })
    })
    
    Array.from(interests).slice(0, 3).forEach(interest => {
      interestSuggestions.push({
        type: 'interest',
        text: interest,
        icon: '🎯'
      })
    })

    // 标签建议
    const tagSuggestions = []
    const tags = new Set()
    users.forEach(user => {
      if (user.tags) {
        user.tags.forEach(tag => {
          if (tag.toLowerCase().includes(lowerQuery)) {
            tags.add(tag)
          }
        })
      }
    })
    
    Array.from(tags).slice(0, 3).forEach(tag => {
      tagSuggestions.push({
        type: 'tag',
        text: tag,
        icon: '🏷️'
      })
    })

    // MBTI 类型建议
    const mbtiSuggestions = []
    const mbtiTypes = ['ENFP', 'INTJ', 'INFJ', 'ENFJ', 'ENTP', 'INFP', 'ISFP', 'ISTP', 'ESFP', 'ESTP', 'ENTJ', 'ESTJ', 'ISFJ', 'ISTJ', 'ESFJ', 'INTP']
    mbtiTypes.forEach(type => {
      if (type.toLowerCase().includes(lowerQuery)) {
        mbtiSuggestions.push({
          type: 'mbti',
          text: type,
          icon: '🧠'
        })
      }
    })

    return [
      ...userSuggestions,
      ...interestSuggestions,
      ...tagSuggestions,
      ...mbtiSuggestions
    ].slice(0, 8)
  }
}

// 快捷操作管理
export class ShortcutManager {
  static shortcuts = [
    {
      key: 'ctrl+k',
      description: '快速搜索',
      action: 'search'
    },
    {
      key: 'ctrl+m',
      description: '查看消息',
      action: 'messages'
    },
    {
      key: 'ctrl+p',
      description: '个人资料',
      action: 'profile'
    },
    {
      key: 'ctrl+t',
      description: '切换主题',
      action: 'theme'
    },
    {
      key: 'ctrl+h',
      description: '返回首页',
      action: 'home'
    }
  ]

  /**
   * 初始化快捷键
   * @param {Object} router - Vue Router 实例
   */
  static init(router) {
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'k':
            event.preventDefault()
            this.triggerSearch()
            break
          case 'm':
            event.preventDefault()
            router.push('/messages')
            break
          case 'p':
            event.preventDefault()
            router.push('/profile')
            break
          case 't':
            event.preventDefault()
            ThemeManager.nextTheme()
            break
          case 'h':
            event.preventDefault()
            router.push('/')
            break
        }
      }
    })
  }

  /**
   * 触发搜索
   */
  static triggerSearch() {
    // 触发搜索框聚焦
    const searchInput = document.querySelector('input[type="text"][placeholder*="搜索"]')
    if (searchInput) {
      searchInput.focus()
      searchInput.select()
    }
  }

  /**
   * 获取快捷键列表
   * @returns {Array} 快捷键列表
   */
  static getShortcuts() {
    return this.shortcuts
  }
}

export default {
  ThemeManager,
  SearchSuggestionManager,
  ShortcutManager,
  THEMES
}
