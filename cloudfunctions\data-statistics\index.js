const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const db = app.database();

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  try {
    const { action } = event;
    
    switch (action) {
      case 'getUserStatistics':
        // 获取用户统计信息
        const totalUsersResult = await db.collection('users').count();
        const totalUsers = totalUsersResult.total;
        
        // 在线用户数
        const onlineUsersResult = await db.collection('users')
          .where({ isOnline: true })
          .count();
        const onlineUsers = onlineUsersResult.total;
        
        // 新用户数（最近30天）
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const newUsersResult = await db.collection('users')
          .where({
            createdAt: db.command.gte(thirtyDaysAgo)
          })
          .count();
        const newUsers = newUsersResult.total;
        
        return {
          success: true,
          data: {
            totalUsers,
            onlineUsers,
            newUsers
          }
        };
        
      case 'getMBTIDistribution':
        // 获取 MBTI 类型分布
        const allUsersResult = await db.collection('users').get();
        const mbtiDistribution = {};
        
        // 初始化所有 MBTI 类型
        const mbtiTypes = ['ENFP', 'INTJ', 'INFJ', 'ENFJ', 'ENTP', 'INFP', 'ISFP', 'ISTP', 'ESFP', 'ESTP', 'ENTJ', 'ESTJ', 'ISFJ', 'ISTJ', 'ESFJ', 'INTP'];
        mbtiTypes.forEach(type => {
          mbtiDistribution[type] = 0;
        });
        
        // 统计分布
        allUsersResult.data.forEach(user => {
          if (mbtiDistribution[user.mbtiType] !== undefined) {
            mbtiDistribution[user.mbtiType]++;
          }
        });
        
        return {
          success: true,
          data: mbtiDistribution
        };
        
      case 'getPopularTags':
        // 获取热门标签
        const usersWithTagsResult = await db.collection('users')
          .where({
            tags: db.command.exists(true)
          })
          .get();
        
        const tagCounts = {};
        usersWithTagsResult.data.forEach(user => {
          if (user.tags && Array.isArray(user.tags)) {
            user.tags.forEach(tag => {
              tagCounts[tag] = (tagCounts[tag] || 0) + 1;
            });
          }
        });
        
        const popularTags = Object.entries(tagCounts)
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 20);
        
        return {
          success: true,
          data: popularTags
        };
        
      case 'getPopularInterests':
        // 获取热门兴趣
        const allUsersForInterestsResult = await db.collection('users').get();
        const interestCounts = {};
        
        allUsersForInterestsResult.data.forEach(user => {
          if (user.interests && Array.isArray(user.interests)) {
            user.interests.forEach(interest => {
              interestCounts[interest] = (interestCounts[interest] || 0) + 1;
            });
          }
        });
        
        const popularInterests = Object.entries(interestCounts)
          .map(([name, count]) => ({ name, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 15);
        
        return {
          success: true,
          data: popularInterests
        };
        
      case 'getAgeDistribution':
        // 获取年龄分布
        const allUsersForAgeResult = await db.collection('users').get();
        const ageDistribution = {
          '18-22': 0,
          '23-27': 0,
          '28-32': 0,
          '33-37': 0,
          '38+': 0
        };
        
        allUsersForAgeResult.data.forEach(user => {
          if (user.age <= 22) ageDistribution['18-22']++;
          else if (user.age <= 27) ageDistribution['23-27']++;
          else if (user.age <= 32) ageDistribution['28-32']++;
          else if (user.age <= 37) ageDistribution['33-37']++;
          else ageDistribution['38+']++;
        });
        
        return {
          success: true,
          data: ageDistribution
        };
        
      case 'getGenderDistribution':
        // 获取性别分布
        const allUsersForGenderResult = await db.collection('users').get();
        const genderDistribution = { '男': 0, '女': 0, '其他': 0 };
        
        allUsersForGenderResult.data.forEach(user => {
          genderDistribution[user.gender] = (genderDistribution[user.gender] || 0) + 1;
        });
        
        return {
          success: true,
          data: genderDistribution
        };
        
      case 'getActivityStatistics':
        // 获取活跃度统计
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        // 今日活跃
        const todayActiveResult = await db.collection('users')
          .where({
            lastActive: db.command.gte(today)
          })
          .count();
        
        // 本周活跃
        const weekActiveResult = await db.collection('users')
          .where({
            lastActive: db.command.gte(thisWeek)
          })
          .count();
        
        // 本月活跃
        const monthActiveResult = await db.collection('users')
          .where({
            lastActive: db.command.gte(thisMonth)
          })
          .count();
        
        // 新用户（本月）
        const newUsersThisMonthResult = await db.collection('users')
          .where({
            createdAt: db.command.gte(thisMonth)
          })
          .count();
        
        return {
          success: true,
          data: {
            today: todayActiveResult.total,
            thisWeek: weekActiveResult.total,
            thisMonth: monthActiveResult.total,
            newUsers: newUsersThisMonthResult.total
          }
        };
        
      case 'getMatchStatistics':
        // 获取匹配统计
        const matchHistoryResult = await db.collection('match_history').get();
        
        let highMatches = 0;
        let mediumMatches = 0;
        let lowMatches = 0;
        let totalMatches = matchHistoryResult.data.length;
        
        matchHistoryResult.data.forEach(match => {
          if (match.matchScore >= 80) highMatches++;
          else if (match.matchScore >= 60) mediumMatches++;
          else lowMatches++;
        });
        
        const matchSuccessRate = {
          high: totalMatches > 0 ? Math.round((highMatches / totalMatches) * 100) : 0,
          medium: totalMatches > 0 ? Math.round((mediumMatches / totalMatches) * 100) : 0,
          low: totalMatches > 0 ? Math.round((lowMatches / totalMatches) * 100) : 0
        };
        
        return {
          success: true,
          data: {
            totalMatches,
            matchSuccessRate,
            averageMatchScore: totalMatches > 0 ? Math.round(
              matchHistoryResult.data.reduce((sum, match) => sum + match.matchScore, 0) / totalMatches
            ) : 0
          }
        };
        
      case 'getAllStatistics':
        // 获取所有统计信息（综合接口）
        const [
          userStats,
          mbtiDist,
          popularTagsData,
          popularInterestsData,
          ageDist,
          genderDist,
          activityStats,
          matchStats
        ] = await Promise.all([
          exports.main({ action: 'getUserStatistics' }),
          exports.main({ action: 'getMBTIDistribution' }),
          exports.main({ action: 'getPopularTags' }),
          exports.main({ action: 'getPopularInterests' }),
          exports.main({ action: 'getAgeDistribution' }),
          exports.main({ action: 'getGenderDistribution' }),
          exports.main({ action: 'getActivityStatistics' }),
          exports.main({ action: 'getMatchStatistics' })
        ]);
        
        return {
          success: true,
          data: {
            userStatistics: userStats.data,
            mbtiDistribution: mbtiDist.data,
            popularTags: popularTagsData.data,
            popularInterests: popularInterestsData.data,
            ageDistribution: ageDist.data,
            genderDistribution: genderDist.data,
            activityStatistics: activityStats.data,
            matchStatistics: matchStats.data
          }
        };
        
      default:
        return {
          success: false,
          message: '不支持的操作'
        };
    }
    
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: error.message
    };
  }
};
