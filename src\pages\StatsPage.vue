<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-100 py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              📊 数据统计
            </span>
          </h1>
        </div>
        
        <button 
          class="btn btn-outline btn-sm"
          @click="refreshStats"
        >
          🔄 刷新数据
        </button>
      </div>

      <!-- 总体统计 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div class="card bg-white/80 backdrop-blur-sm shadow-lg">
          <div class="card-body text-center p-4">
            <div class="text-3xl text-purple-600 mb-2">{{ totalUsers }}</div>
            <div class="text-sm text-gray-600">总用户数</div>
          </div>
        </div>
        
        <div class="card bg-white/80 backdrop-blur-sm shadow-lg">
          <div class="card-body text-center p-4">
            <div class="text-3xl text-pink-600 mb-2">{{ onlineUsers }}</div>
            <div class="text-sm text-gray-600">在线用户</div>
          </div>
        </div>
        
        <div class="card bg-white/80 backdrop-blur-sm shadow-lg">
          <div class="card-body text-center p-4">
            <div class="text-3xl text-indigo-600 mb-2">{{ totalMatches }}</div>
            <div class="text-sm text-gray-600">总匹配数</div>
          </div>
        </div>
        
        <div class="card bg-white/80 backdrop-blur-sm shadow-lg">
          <div class="card-body text-center p-4">
            <div class="text-3xl text-green-600 mb-2">{{ averageMatchScore }}%</div>
            <div class="text-sm text-gray-600">平均匹配度</div>
          </div>
        </div>
      </div>

      <!-- MBTI 类型分布 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
        <div class="card-body">
          <h3 class="text-xl font-bold text-gray-800 mb-6">🧠 MBTI 类型分布</h3>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div 
              v-for="(count, type) in mbtiDistribution" 
              :key="type"
              class="text-center"
            >
              <div 
                class="w-full h-24 rounded-lg flex items-center justify-center text-white font-bold text-lg mb-2"
                :class="getMBTIColor(type)"
              >
                {{ type }}
              </div>
              <div class="text-2xl font-bold text-gray-800">{{ count }}</div>
              <div class="text-sm text-gray-600">{{ ((count / totalUsers) * 100).toFixed(1) }}%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 热门标签 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
        <div class="card-body">
          <h3 class="text-xl font-bold text-gray-800 mb-6">🏷️ 热门标签</h3>
          
          <div class="flex flex-wrap gap-3">
            <div 
              v-for="tag in popularTags" 
              :key="tag.name"
              class="badge badge-lg gap-2"
              :class="getTagColor(tag.count)"
            >
              <span>{{ tag.name }}</span>
              <span class="badge badge-sm bg-white/30">{{ tag.count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 兴趣爱好统计 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
        <div class="card-body">
          <h3 class="text-xl font-bold text-gray-800 mb-6">🎯 热门兴趣</h3>
          
          <div class="space-y-3">
            <div 
              v-for="interest in popularInterests" 
              :key="interest.name"
              class="flex items-center justify-between"
            >
              <span class="font-medium">{{ interest.name }}</span>
              <div class="flex items-center space-x-3">
                <div class="w-32 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
                    :style="`width: ${(interest.count / totalUsers) * 100}%`"
                  ></div>
                </div>
                <span class="text-sm text-gray-600 w-12 text-right">{{ interest.count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 年龄分布 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
        <div class="card-body">
          <h3 class="text-xl font-bold text-gray-800 mb-6">📈 年龄分布</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div 
              v-for="(count, range) in ageDistribution" 
              :key="range"
              class="text-center"
            >
              <div class="text-2xl font-bold text-purple-600">{{ count }}</div>
              <div class="text-sm text-gray-600">{{ range }}岁</div>
              <div class="text-xs text-gray-500">{{ ((count / totalUsers) * 100).toFixed(1) }}%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 性别分布 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
        <div class="card-body">
          <h3 class="text-xl font-bold text-gray-800 mb-6">⚧️ 性别分布</h3>
          
          <div class="grid grid-cols-3 gap-6">
            <div 
              v-for="(count, gender) in genderDistribution" 
              :key="gender"
              class="text-center"
            >
              <div class="text-4xl mb-2">
                {{ gender === '男' ? '👨' : gender === '女' ? '👩' : '🧑' }}
              </div>
              <div class="text-2xl font-bold text-gray-800">{{ count }}</div>
              <div class="text-sm text-gray-600">{{ gender }}</div>
              <div class="text-xs text-gray-500">{{ ((count / totalUsers) * 100).toFixed(1) }}%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 活跃度统计 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
        <div class="card-body">
          <h3 class="text-xl font-bold text-gray-800 mb-6">⚡ 用户活跃度</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{{ activityStats.today }}</div>
              <div class="text-sm text-gray-600">今日活跃</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ activityStats.thisWeek }}</div>
              <div class="text-sm text-gray-600">本周活跃</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">{{ activityStats.thisMonth }}</div>
              <div class="text-sm text-gray-600">本月活跃</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-600">{{ activityStats.newUsers }}</div>
              <div class="text-sm text-gray-600">新用户</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 匹配成功率 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
        <div class="card-body">
          <h3 class="text-xl font-bold text-gray-800 mb-6">💕 匹配成功率</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div 
                class="radial-progress text-green-600 text-2xl font-bold mb-4"
                :style="`--value:${matchSuccessRate.high}; --size:6rem; --thickness: 8px;`"
              >
                {{ matchSuccessRate.high }}%
              </div>
              <div class="text-sm text-gray-600">高匹配度 (80%+)</div>
            </div>
            <div class="text-center">
              <div 
                class="radial-progress text-yellow-600 text-2xl font-bold mb-4"
                :style="`--value:${matchSuccessRate.medium}; --size:6rem; --thickness: 8px;`"
              >
                {{ matchSuccessRate.medium }}%
              </div>
              <div class="text-sm text-gray-600">中等匹配度 (60-80%)</div>
            </div>
            <div class="text-center">
              <div 
                class="radial-progress text-red-600 text-2xl font-bold mb-4"
                :style="`--value:${matchSuccessRate.low}; --size:6rem; --thickness: 8px;`"
              >
                {{ matchSuccessRate.low }}%
              </div>
              <div class="text-sm text-gray-600">低匹配度 (60%以下)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import LocalStorageManager from '../utils/localStorage.js'
import { getMBTIColor, getAllMBTITypes } from '../utils/mbtiTypes.js'

const router = useRouter()

// 响应式数据
const allUsers = ref([])
const currentUser = ref(null)

// 计算属性
const totalUsers = computed(() => allUsers.value.length)

const onlineUsers = computed(() => 
  allUsers.value.filter(user => user.isOnline).length
)

const totalMatches = computed(() => {
  if (!currentUser.value) return 0
  return allUsers.value.reduce((total, user) => {
    if (user.id === currentUser.value.id) return total
    const matches = LocalStorageManager.getMatches(user, 10)
    return total + matches.length
  }, 0)
})

const averageMatchScore = computed(() => {
  if (!currentUser.value || totalMatches.value === 0) return 0
  
  let totalScore = 0
  let matchCount = 0
  
  allUsers.value.forEach(user => {
    if (user.id === currentUser.value.id) return
    const matches = LocalStorageManager.getMatches(user, 10)
    matches.forEach(match => {
      totalScore += match.matchScore
      matchCount++
    })
  })
  
  return matchCount > 0 ? Math.round(totalScore / matchCount) : 0
})

const mbtiDistribution = computed(() => {
  const distribution = {}
  getAllMBTITypes().forEach(type => {
    distribution[type] = 0
  })
  
  allUsers.value.forEach(user => {
    if (distribution[user.mbtiType] !== undefined) {
      distribution[user.mbtiType]++
    }
  })
  
  return distribution
})

const popularTags = computed(() => {
  const tagCounts = {}
  
  allUsers.value.forEach(user => {
    if (user.tags) {
      user.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1
      })
    }
  })
  
  return Object.entries(tagCounts)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 20)
})

const popularInterests = computed(() => {
  const interestCounts = {}
  
  allUsers.value.forEach(user => {
    user.interests.forEach(interest => {
      interestCounts[interest] = (interestCounts[interest] || 0) + 1
    })
  })
  
  return Object.entries(interestCounts)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 15)
})

const ageDistribution = computed(() => {
  const distribution = {
    '18-22': 0,
    '23-27': 0,
    '28-32': 0,
    '33-37': 0,
    '38+': 0
  }
  
  allUsers.value.forEach(user => {
    if (user.age <= 22) distribution['18-22']++
    else if (user.age <= 27) distribution['23-27']++
    else if (user.age <= 32) distribution['28-32']++
    else if (user.age <= 37) distribution['33-37']++
    else distribution['38+']++
  })
  
  return distribution
})

const genderDistribution = computed(() => {
  const distribution = { '男': 0, '女': 0, '其他': 0 }
  
  allUsers.value.forEach(user => {
    distribution[user.gender] = (distribution[user.gender] || 0) + 1
  })
  
  return distribution
})

const activityStats = computed(() => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
  const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
  
  let todayActive = 0
  let weekActive = 0
  let monthActive = 0
  let newUsers = 0
  
  allUsers.value.forEach(user => {
    const lastActive = new Date(user.lastActive || user.createdAt)
    const createdAt = new Date(user.createdAt)
    
    if (lastActive >= today) todayActive++
    if (lastActive >= thisWeek) weekActive++
    if (lastActive >= thisMonth) monthActive++
    if (createdAt >= thisMonth) newUsers++
  })
  
  return {
    today: todayActive,
    thisWeek: weekActive,
    thisMonth: monthActive,
    newUsers
  }
})

const matchSuccessRate = computed(() => {
  if (!currentUser.value) return { high: 0, medium: 0, low: 0 }
  
  let highCount = 0
  let mediumCount = 0
  let lowCount = 0
  let totalCount = 0
  
  allUsers.value.forEach(user => {
    if (user.id === currentUser.value.id) return
    const matches = LocalStorageManager.getMatches(user, 10)
    matches.forEach(match => {
      totalCount++
      if (match.matchScore >= 80) highCount++
      else if (match.matchScore >= 60) mediumCount++
      else lowCount++
    })
  })
  
  if (totalCount === 0) return { high: 0, medium: 0, low: 0 }
  
  return {
    high: Math.round((highCount / totalCount) * 100),
    medium: Math.round((mediumCount / totalCount) * 100),
    low: Math.round((lowCount / totalCount) * 100)
  }
})

// 方法
const getTagColor = (count) => {
  if (count >= 5) return 'badge-primary'
  if (count >= 3) return 'badge-secondary'
  if (count >= 2) return 'badge-accent'
  return 'badge-ghost'
}

const refreshStats = () => {
  loadData()
}

const loadData = () => {
  allUsers.value = LocalStorageManager.getAllUsers()
  currentUser.value = LocalStorageManager.getCurrentUser()
}

// 组件挂载时初始化
onMounted(() => {
  loadData()
})
</script>
