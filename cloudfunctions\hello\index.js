const cloud = require('@cloudbase/node-sdk')

// 初始化云开发实例
const app = cloud.init({
  env: cloud.SYMBOL_CURRENT_ENV
})

// 获取数据库引用
const db = app.database()

// MBTI 兼容性矩阵 - 基于心理学研究的匹配度
const MBTI_COMPATIBILITY = {
  'ENFP': { 'INTJ': 95, 'INFJ': 90, 'ENFJ': 85, 'ENTP': 80, 'ISFP': 75, 'ISTP': 70, 'ESFP': 65, 'ESTP': 60, 'INFP': 85, 'INTP': 75, 'ENTJ': 70, 'ESTJ': 50, 'ISFJ': 60, 'ISTJ': 45, 'ESFJ': 55, 'ENFP': 70 },
  'INTJ': { 'ENFP': 95, 'ENTP': 90, 'INFP': 85, 'INTP': 80, 'ENFJ': 75, 'ENTJ': 70, 'INFJ': 85, 'ISFP': 65, 'ISTP': 75, 'ESFP': 50, 'ESTP': 55, 'ESTJ': 60, 'ISFJ': 45, 'ISTJ': 70, 'ESFJ': 40, 'INTJ': 75 },
  'INFJ': { 'ENFP': 90, 'ENTP': 85, 'INFP': 80, 'INTJ': 85, 'ENFJ': 75, 'ENTJ': 70, 'ISFP': 70, 'ISTP': 65, 'ESFP': 55, 'ESTP': 50, 'ESTJ': 55, 'ISFJ': 70, 'ISTJ': 65, 'ESFJ': 60, 'INTP': 75, 'INFJ': 70 },
  'ENFJ': { 'INFP': 90, 'ISFP': 85, 'ENFP': 85, 'INFJ': 75, 'INTP': 80, 'INTJ': 75, 'ENTP': 70, 'ISTP': 65, 'ESFP': 75, 'ESTP': 70, 'ESTJ': 65, 'ISFJ': 80, 'ISTJ': 60, 'ESFJ': 75, 'ENTJ': 65, 'ENFJ': 70 },
  'ENTP': { 'INTJ': 90, 'INFJ': 85, 'ENFJ': 70, 'ENFP': 80, 'INTP': 85, 'INFP': 75, 'ISFP': 60, 'ISTP': 70, 'ESFP': 65, 'ESTP': 75, 'ENTJ': 80, 'ESTJ': 70, 'ISFJ': 55, 'ISTJ': 50, 'ESFJ': 60, 'ENTP': 75 },
  'INFP': { 'ENFJ': 90, 'ENTJ': 85, 'INTJ': 85, 'ENTP': 75, 'ENFP': 85, 'INFJ': 80, 'ISFP': 75, 'ISTP': 70, 'ESFP': 60, 'ESTP': 55, 'ESTJ': 50, 'ISFJ': 65, 'ISTJ': 55, 'ESFJ': 60, 'INTP': 70, 'INFP': 75 },
  'ISFP': { 'ENFJ': 85, 'ESFJ': 80, 'ENFP': 75, 'INFP': 75, 'ISFJ': 70, 'ESFP': 75, 'ESTP': 70, 'ESTJ': 65, 'INTJ': 65, 'INFJ': 70, 'ENTP': 60, 'INTP': 65, 'ENTJ': 60, 'ISTJ': 70, 'ISTP': 65, 'ISFP': 70 },
  'ISTP': { 'ESFJ': 85, 'ENFJ': 65, 'ESTP': 80, 'ESTJ': 75, 'ISFJ': 70, 'ESFP': 75, 'ENFP': 70, 'INFP': 70, 'INTJ': 75, 'INFJ': 65, 'ENTP': 70, 'INTP': 75, 'ENTJ': 70, 'ISTJ': 65, 'ISFP': 65, 'ISTP': 70 },
  'ESFP': { 'ISFJ': 85, 'ISTJ': 80, 'ESFJ': 75, 'ISFP': 75, 'ESTP': 70, 'ESTJ': 70, 'ENFJ': 75, 'INFJ': 55, 'ENFP': 65, 'INFP': 60, 'INTJ': 50, 'ENTP': 65, 'INTP': 55, 'ENTJ': 60, 'ISTP': 75, 'ESFP': 70 },
  'ESTP': { 'ISFJ': 80, 'ISTJ': 75, 'ESFJ': 70, 'ESTJ': 75, 'ESFP': 70, 'ISTP': 80, 'ENFJ': 70, 'INFJ': 50, 'ENFP': 60, 'INFP': 55, 'INTJ': 55, 'ENTP': 75, 'INTP': 60, 'ENTJ': 70, 'ISFP': 70, 'ESTP': 75 },
  'ENTJ': { 'INFP': 85, 'INTP': 80, 'ENTP': 80, 'INTJ': 70, 'ENFP': 70, 'INFJ': 70, 'ENFJ': 65, 'ISFP': 60, 'ISTP': 70, 'ESFP': 60, 'ESTP': 70, 'ESTJ': 75, 'ISFJ': 55, 'ISTJ': 65, 'ESFJ': 60, 'ENTJ': 70 },
  'ESTJ': { 'ISFP': 65, 'ISTP': 75, 'ISFJ': 80, 'ISTJ': 85, 'ESFP': 70, 'ESTP': 75, 'ESFJ': 75, 'ENFJ': 65, 'INFJ': 55, 'ENFP': 50, 'INFP': 50, 'INTJ': 60, 'ENTP': 70, 'INTP': 55, 'ENTJ': 75, 'ESTJ': 70 },
  'ISFJ': { 'ESFP': 85, 'ESTP': 80, 'ENFJ': 80, 'ESFJ': 75, 'ISFP': 70, 'ISTP': 70, 'ESTJ': 80, 'ISTJ': 75, 'ENFP': 60, 'INFP': 65, 'INTJ': 45, 'INFJ': 70, 'ENTP': 55, 'INTP': 60, 'ENTJ': 55, 'ISFJ': 70 },
  'ISTJ': { 'ESFP': 80, 'ESTP': 75, 'ESFJ': 80, 'ESTJ': 85, 'ISFP': 70, 'ISTP': 65, 'ISFJ': 75, 'ENFJ': 60, 'INFJ': 65, 'ENFP': 45, 'INFP': 55, 'INTJ': 70, 'ENTP': 50, 'INTP': 65, 'ENTJ': 65, 'ISTJ': 75 },
  'ESFJ': { 'ISFP': 80, 'ISTP': 85, 'ISFJ': 75, 'ISTJ': 80, 'ESFP': 75, 'ESTP': 70, 'ESTJ': 75, 'ENFJ': 75, 'INFJ': 60, 'ENFP': 55, 'INFP': 60, 'INTJ': 40, 'ENTP': 60, 'INTP': 50, 'ENTJ': 60, 'ESFJ': 70 },
  'INTP': { 'ENTJ': 80, 'ENFJ': 80, 'ENTP': 85, 'INTJ': 80, 'ENFP': 75, 'INFJ': 75, 'INFP': 70, 'ISFP': 65, 'ISTP': 75, 'ESFP': 55, 'ESTP': 60, 'ESTJ': 55, 'ISFJ': 60, 'ISTJ': 65, 'ESFJ': 50, 'INTP': 75 }
}

/**
 * 计算两个 MBTI 类型的匹配度
 * @param {string} type1 - 第一个 MBTI 类型
 * @param {string} type2 - 第二个 MBTI 类型
 * @returns {number} 匹配度 (0-100)
 */
function calculateMBTICompatibility(type1, type2) {
  if (!MBTI_COMPATIBILITY[type1] || !MBTI_COMPATIBILITY[type1][type2]) {
    return 50 // 默认匹配度
  }
  return MBTI_COMPATIBILITY[type1][type2]
}

/**
 * 计算综合匹配度（包含年龄、兴趣等因素）
 * @param {Object} user1 - 用户1信息
 * @param {Object} user2 - 用户2信息
 * @returns {number} 综合匹配度 (0-100)
 */
function calculateOverallCompatibility(user1, user2) {
  // MBTI 匹配度权重 70%
  const mbtiScore = calculateMBTICompatibility(user1.mbtiType, user2.mbtiType) * 0.7

  // 年龄匹配度权重 15%
  const ageDiff = Math.abs(user1.age - user2.age)
  const ageScore = Math.max(0, 100 - ageDiff * 5) * 0.15

  // 兴趣匹配度权重 15%
  const commonInterests = user1.interests.filter(interest =>
    user2.interests.includes(interest)
  ).length
  const totalInterests = new Set([...user1.interests, ...user2.interests]).size
  const interestScore = totalInterests > 0 ? (commonInterests / totalInterests * 100) * 0.15 : 0

  return Math.round(mbtiScore + ageScore + interestScore)
}

/**
 * MBTI 匹配云函数入口函数
 * @param {Object} event - 云函数的参数
 * @param {Object} context - 云函数的上下文信息
 */
exports.main = async (event, context) => {
  try {
    const { action, data } = event
    console.log('MBTI 匹配云函数被调用，参数：', event)

    switch (action) {
      case 'addUser':
        return await addUser(data)
      case 'getMatches':
        return await getMatches(data)
      case 'getAllUsers':
        return await getAllUsers()
      case 'getUserById':
        return await getUserById(data.id)
      case 'updateUser':
        return await updateUser(data)
      case 'deleteUser':
        return await deleteUser(data.id)
      default:
        return {
          code: -1,
          message: '不支持的操作类型',
          data: null
        }
    }
  } catch (error) {
    console.error('MBTI 匹配云函数执行出错：', error)
    return {
      code: -1,
      message: error.message,
      data: null
    }
  }
}