<template>
  <div class="min-h-screen bg-theme-gradient py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              👤 我的资料
            </span>
          </h1>
        </div>
        
        <button 
          class="btn btn-outline btn-sm"
          @click="logout"
        >
          退出登录
        </button>
      </div>

      <div v-if="currentUser" class="max-w-4xl mx-auto">
        <!-- 个人信息卡片 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
          <div class="card-body">
            <div class="flex flex-col lg:flex-row lg:items-center space-y-6 lg:space-y-0 lg:space-x-8">
              <!-- 头像和在线状态 -->
              <div class="flex-shrink-0 text-center">
                <div class="avatar relative">
                  <div class="w-32 h-32 rounded-full ring ring-purple-200 ring-offset-4">
                    <img :src="currentUser.avatar" :alt="currentUser.name" />
                  </div>
                  <!-- 在线状态指示器 -->
                  <div
                    class="absolute bottom-2 right-2 w-6 h-6 rounded-full border-4 border-white"
                    :class="currentUser.isOnline ? 'bg-green-500' : 'bg-gray-400'"
                  ></div>
                </div>
                <div class="mt-4 space-y-2">
                  <button
                    class="btn btn-ghost btn-sm"
                    @click="showAvatarUpload = true"
                  >
                    📷 更换头像
                  </button>
                  <div class="text-xs text-gray-500">
                    {{ currentUser.isOnline ? '在线' : getLastActiveText(currentUser.lastActive) }}
                  </div>
                </div>
              </div>

              <!-- 基本信息 -->
              <div class="flex-1 space-y-4">
                <div>
                  <div class="flex items-center space-x-3 mb-2">
                    <h2 class="text-3xl font-bold text-gray-800">{{ currentUser.name }}</h2>
                    <button
                      class="btn btn-circle btn-sm"
                      :class="currentUser.isOnline ? 'btn-success' : 'btn-ghost'"
                      @click="toggleOnlineStatus"
                      :title="currentUser.isOnline ? '设为离线' : '设为在线'"
                    >
                      <div
                        class="w-3 h-3 rounded-full"
                        :class="currentUser.isOnline ? 'bg-white' : 'bg-green-500'"
                      ></div>
                    </button>
                  </div>

                  <div class="flex flex-wrap items-center gap-3 mt-2">
                    <span
                      class="badge text-white text-lg px-4 py-3"
                      :class="getMBTIColor(currentUser.mbtiType)"
                    >
                      {{ currentUser.mbtiType }} - {{ getMBTIName(currentUser.mbtiType) }}
                    </span>
                    <span class="badge badge-outline badge-lg">{{ currentUser.age }}岁</span>
                    <span class="badge badge-outline badge-lg">{{ currentUser.gender }}</span>
                  </div>
                </div>

                <!-- 个人标签 -->
                <div v-if="currentUser.tags && currentUser.tags.length > 0">
                  <h3 class="font-semibold text-gray-700 mb-2">个人标签</h3>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="tag in currentUser.tags"
                      :key="tag"
                      class="badge badge-secondary text-white"
                    >
                      {{ tag }}
                    </span>
                  </div>
                </div>

                <div v-if="currentUser.bio">
                  <h3 class="font-semibold text-gray-700 mb-2">自我介绍</h3>
                  <p class="text-gray-600">{{ currentUser.bio }}</p>
                </div>

                <div>
                  <h3 class="font-semibold text-gray-700 mb-2">兴趣爱好</h3>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="interest in currentUser.interests"
                      :key="interest"
                      class="badge badge-primary text-white"
                    >
                      {{ interest }}
                    </span>
                  </div>
                </div>

                <!-- 社交链接 -->
                <div v-if="currentUser.socialLinks && Object.keys(currentUser.socialLinks).length > 0">
                  <h3 class="font-semibold text-gray-700 mb-2">社交链接</h3>
                  <div class="flex flex-wrap gap-3">
                    <a
                      v-for="(url, platform) in currentUser.socialLinks"
                      :key="platform"
                      :href="url"
                      target="_blank"
                      class="btn btn-outline btn-sm"
                    >
                      <span class="capitalize">{{ platform }}</span>
                      <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                      </svg>
                    </a>
                  </div>
                </div>

                <div class="text-sm text-gray-500">
                  加入时间：{{ formatDate(currentUser.createdAt) }}
                </div>
              </div>

              <!-- 编辑按钮 -->
              <div class="flex-shrink-0 space-y-2">
                <button
                  class="btn btn-primary block w-full"
                  @click="startEdit"
                >
                  ✏️ 编辑资料
                </button>
                <button
                  class="btn btn-outline block w-full"
                  @click="showPrivacySettings = true"
                >
                  🔒 隐私设置
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
          <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
            <div class="card-body text-center p-4">
              <div class="text-2xl text-purple-600 mb-1">{{ matchCount }}</div>
              <div class="text-xs text-gray-600">潜在匹配</div>
            </div>
          </div>

          <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
            <div class="card-body text-center p-4">
              <div class="text-2xl text-pink-600 mb-1">{{ averageMatchScore }}%</div>
              <div class="text-xs text-gray-600">平均匹配度</div>
            </div>
          </div>

          <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
            <div class="card-body text-center p-4">
              <div class="text-2xl text-indigo-600 mb-1">{{ followersCount }}</div>
              <div class="text-xs text-gray-600">粉丝</div>
            </div>
          </div>

          <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
            <div class="card-body text-center p-4">
              <div class="text-2xl text-green-600 mb-1">{{ followingCount }}</div>
              <div class="text-xs text-gray-600">关注</div>
            </div>
          </div>

          <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
            <div class="card-body text-center p-4">
              <div class="text-2xl text-orange-600 mb-1">{{ visitorCount }}</div>
              <div class="text-xs text-gray-600">访客</div>
            </div>
          </div>
        </div>

        <!-- 访客记录 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
          <div class="card-body">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-xl font-bold text-gray-800">👥 最近访客</h3>
              <button
                class="btn btn-outline btn-sm"
                @click="refreshVisitors"
              >
                🔄 刷新
              </button>
            </div>

            <div v-if="recentVisitors.length > 0" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div
                v-for="visitor in recentVisitors"
                :key="visitor.visitorId"
                class="text-center cursor-pointer hover:bg-gray-50 p-3 rounded-lg transition-colors"
                @click="viewVisitorProfile(visitor.visitor)"
              >
                <div class="avatar mb-2">
                  <div class="w-12 h-12 rounded-full">
                    <img :src="visitor.visitor.avatar" :alt="visitor.visitor.name" />
                  </div>
                </div>
                <div class="text-sm font-medium text-gray-800 truncate">{{ visitor.visitor.name }}</div>
                <div class="text-xs text-gray-500">{{ formatVisitTime(visitor.visitTime) }}</div>
              </div>
            </div>

            <div v-else class="text-center py-8">
              <div class="text-4xl mb-4">👻</div>
              <p class="text-gray-600">还没有访客记录</p>
            </div>
          </div>
        </div>

        <!-- 最佳匹配预览 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-xl font-bold text-gray-800">💝 你的最佳匹配</h3>
              <button 
                class="btn btn-outline btn-sm"
                @click="$router.push('/matches')"
              >
                查看全部
              </button>
            </div>

            <div v-if="topMatches.length > 0" class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div 
                v-for="match in topMatches" 
                :key="match.id"
                class="card bg-gradient-to-br from-purple-50 to-pink-50 shadow-md hover:shadow-lg transition-all duration-300"
              >
                <div class="card-body p-4">
                  <div class="flex items-center space-x-3 mb-3">
                    <div class="avatar">
                      <div class="w-12 h-12 rounded-full">
                        <img :src="match.avatar" :alt="match.name" />
                      </div>
                    </div>
                    <div>
                      <h4 class="font-bold text-sm">{{ match.name }}</h4>
                      <span class="text-xs text-gray-600">{{ match.mbtiType }}</span>
                    </div>
                  </div>
                  
                  <div class="text-center">
                    <div 
                      class="radial-progress text-primary text-sm"
                      :style="`--value:${match.matchScore}; --size:3rem; --thickness: 4px;`"
                    >
                      {{ match.matchScore }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-8">
              <div class="text-4xl mb-4">😔</div>
              <p class="text-gray-600">暂无匹配结果，等待更多用户加入</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑模态框 -->
      <div v-if="isEditing" class="modal modal-open">
        <div class="modal-box max-w-4xl">
          <h3 class="font-bold text-lg mb-4">编辑个人资料</h3>

          <form @submit.prevent="saveProfile" class="space-y-6">
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">姓名</span>
                </label>
                <input
                  type="text"
                  v-model="editForm.name"
                  class="input input-bordered"
                  required
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">年龄</span>
                </label>
                <input
                  type="number"
                  v-model.number="editForm.age"
                  class="input input-bordered"
                  min="16"
                  max="100"
                  required
                />
              </div>
            </div>

            <!-- 个人标签 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">个人标签</span>
                <span class="label-text-alt">最多选择 5 个</span>
              </label>
              <div class="space-y-3">
                <div class="flex flex-wrap gap-2">
                  <span
                    v-for="tag in editForm.tags"
                    :key="tag"
                    class="badge badge-secondary text-white gap-2"
                  >
                    {{ tag }}
                    <button
                      type="button"
                      class="btn btn-circle btn-xs"
                      @click="removeTag(tag)"
                    >
                      ×
                    </button>
                  </span>
                </div>
                <div class="flex gap-2">
                  <input
                    type="text"
                    v-model="newTag"
                    placeholder="输入新标签..."
                    class="input input-bordered input-sm flex-1"
                    @keyup.enter="addTag"
                  />
                  <button
                    type="button"
                    class="btn btn-outline btn-sm"
                    @click="addTag"
                    :disabled="!newTag.trim() || editForm.tags.length >= 5"
                  >
                    添加
                  </button>
                </div>
                <div class="text-xs text-gray-500">
                  推荐标签：
                  <button
                    v-for="tag in suggestedTags"
                    :key="tag"
                    type="button"
                    class="btn btn-ghost btn-xs ml-1"
                    @click="addSuggestedTag(tag)"
                    :disabled="editForm.tags.includes(tag) || editForm.tags.length >= 5"
                  >
                    {{ tag }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 自我介绍 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">自我介绍</span>
                <span class="label-text-alt">{{ editForm.bio.length }}/200</span>
              </label>
              <textarea
                v-model="editForm.bio"
                class="textarea textarea-bordered h-24"
                maxlength="200"
                placeholder="简单介绍一下自己..."
              ></textarea>
            </div>

            <!-- 兴趣爱好 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">兴趣爱好</span>
                <span class="label-text-alt">至少选择 3 个</span>
              </label>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-2 max-h-48 overflow-y-auto border rounded-lg p-4">
                <label
                  v-for="interest in COMMON_INTERESTS"
                  :key="interest"
                  class="label cursor-pointer justify-start p-2 rounded hover:bg-gray-50"
                >
                  <input
                    type="checkbox"
                    :value="interest"
                    v-model="editForm.interests"
                    class="checkbox checkbox-primary checkbox-sm"
                  />
                  <span class="label-text ml-2 text-sm">{{ interest }}</span>
                </label>
              </div>
            </div>

            <!-- 社交链接 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">社交链接</span>
                <span class="label-text-alt">可选</span>
              </label>
              <div class="space-y-3">
                <div
                  v-for="(platform, index) in socialPlatforms"
                  :key="platform"
                  class="flex gap-2 items-center"
                >
                  <span class="w-20 text-sm font-medium capitalize">{{ platform }}:</span>
                  <input
                    type="url"
                    v-model="editForm.socialLinks[platform]"
                    :placeholder="`https://${platform}.com/username`"
                    class="input input-bordered input-sm flex-1"
                  />
                </div>
              </div>
            </div>

            <div class="modal-action">
              <button
                type="button"
                class="btn btn-ghost"
                @click="cancelEdit"
              >
                取消
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="isSaving || editForm.interests.length < 3"
              >
                <span v-if="isSaving" class="loading loading-spinner loading-sm"></span>
                {{ isSaving ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 头像上传模态框 -->
      <div v-if="showAvatarUpload" class="modal modal-open">
        <div class="modal-box">
          <h3 class="font-bold text-lg mb-4">更换头像</h3>

          <div class="space-y-4">
            <div class="text-center">
              <div class="avatar mb-4">
                <div class="w-32 h-32 rounded-full">
                  <img :src="currentUser.avatar" :alt="currentUser.name" />
                </div>
              </div>
              <p class="text-sm text-gray-600">选择一个新的头像</p>
            </div>

            <!-- 预设头像选择 -->
            <div>
              <h4 class="font-medium mb-3">选择预设头像</h4>
              <div class="grid grid-cols-4 gap-3">
                <div
                  v-for="avatar in presetAvatars"
                  :key="avatar"
                  class="avatar cursor-pointer hover:scale-110 transition-transform"
                  @click="selectAvatar(avatar)"
                >
                  <div class="w-16 h-16 rounded-full ring-2 ring-transparent hover:ring-purple-300">
                    <img :src="avatar" alt="预设头像" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 文件上传 -->
            <div>
              <h4 class="font-medium mb-3">或上传自定义头像</h4>
              <input
                type="file"
                accept="image/*"
                class="file-input file-input-bordered w-full"
                @change="handleAvatarUpload"
              />
              <div class="text-xs text-gray-500 mt-2">
                支持 JPG、PNG 格式，建议尺寸 200x200 像素
              </div>
            </div>
          </div>

          <div class="modal-action">
            <button
              class="btn btn-ghost"
              @click="showAvatarUpload = false"
            >
              取消
            </button>
          </div>
        </div>
      </div>

      <!-- 隐私设置模态框 -->
      <div v-if="showPrivacySettings" class="modal modal-open">
        <div class="modal-box">
          <h3 class="font-bold text-lg mb-4">隐私设置</h3>

          <div class="space-y-4">
            <div class="form-control">
              <label class="label cursor-pointer">
                <span class="label-text">允许其他用户查看我的资料</span>
                <input
                  type="checkbox"
                  v-model="privacySettings.allowProfileView"
                  class="toggle toggle-primary"
                />
              </label>
            </div>

            <div class="form-control">
              <label class="label cursor-pointer">
                <span class="label-text">允许其他用户向我发送消息</span>
                <input
                  type="checkbox"
                  v-model="privacySettings.allowMessages"
                  class="toggle toggle-primary"
                />
              </label>
            </div>

            <div class="form-control">
              <label class="label cursor-pointer">
                <span class="label-text">显示我的在线状态</span>
                <input
                  type="checkbox"
                  v-model="privacySettings.showOnlineStatus"
                  class="toggle toggle-primary"
                />
              </label>
            </div>

            <div class="form-control">
              <label class="label cursor-pointer">
                <span class="label-text">允许在匹配结果中显示</span>
                <input
                  type="checkbox"
                  v-model="privacySettings.showInMatches"
                  class="toggle toggle-primary"
                />
              </label>
            </div>

            <div class="form-control">
              <label class="label cursor-pointer">
                <span class="label-text">记录访客信息</span>
                <input
                  type="checkbox"
                  v-model="privacySettings.recordVisitors"
                  class="toggle toggle-primary"
                />
              </label>
            </div>
          </div>

          <div class="modal-action">
            <button
              class="btn btn-ghost"
              @click="showPrivacySettings = false"
            >
              取消
            </button>
            <button
              class="btn btn-primary"
              @click="savePrivacySettings"
            >
              保存设置
            </button>
          </div>
        </div>
      </div>

      <!-- 无用户状态 -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">😔</div>
        <h3 class="text-2xl font-bold text-gray-600 mb-4">未找到用户信息</h3>
        <p class="text-gray-500 mb-6">请先注册创建你的档案</p>
        <button 
          class="btn btn-primary"
          @click="$router.push('/register')"
        >
          立即注册
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import LocalStorageManager from '../utils/localStorage.js'
import { getMBTIColor, getMBTIName, COMMON_INTERESTS } from '../utils/mbtiTypes.js'

const router = useRouter()

// 响应式数据
const currentUser = ref(null)
const isEditing = ref(false)
const isSaving = ref(false)
const topMatches = ref([])
const recentVisitors = ref([])
const showAvatarUpload = ref(false)
const showPrivacySettings = ref(false)
const newTag = ref('')

// 编辑表单
const editForm = reactive({
  name: '',
  age: null,
  bio: '',
  interests: [],
  tags: [],
  socialLinks: {}
})

// 隐私设置
const privacySettings = reactive({
  allowProfileView: true,
  allowMessages: true,
  showOnlineStatus: true,
  showInMatches: true,
  recordVisitors: true
})

// 社交平台列表
const socialPlatforms = ['github', 'linkedin', 'twitter', 'instagram', 'weibo', 'douban', 'blog']

// 预设头像
const presetAvatars = [
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face'
]

// 推荐标签
const suggestedTags = ['技术达人', '音乐爱好者', '旅行达人', '美食家', '摄影师', '运动健将', '读书爱好者', '电影迷', '艺术家', '创业者']

// 计算属性
const matchCount = computed(() => {
  if (!currentUser.value) return 0
  const matches = LocalStorageManager.getMatches(currentUser.value, 10)
  return matches.length
})

const averageMatchScore = computed(() => {
  if (!currentUser.value) return 0
  const matches = LocalStorageManager.getMatches(currentUser.value, 10)
  if (matches.length === 0) return 0

  const total = matches.reduce((sum, match) => sum + match.matchScore, 0)
  return Math.round(total / matches.length)
})

const followersCount = computed(() => {
  if (!currentUser.value) return 0
  return LocalStorageManager.getFollowers(currentUser.value.id).length
})

const followingCount = computed(() => {
  if (!currentUser.value) return 0
  return LocalStorageManager.getFollowing(currentUser.value.id).length
})

const visitorCount = computed(() => {
  return recentVisitors.value.length
})

// 方法
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getLastActiveText = (lastActive) => {
  if (!lastActive) return '很久之前'

  const now = new Date()
  const lastActiveDate = new Date(lastActive)
  const diffMs = now - lastActiveDate
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  return '很久之前'
}

const formatVisitTime = (visitTime) => {
  const now = new Date()
  const visitDate = new Date(visitTime)
  const diffMs = now - visitDate
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  return visitDate.toLocaleDateString('zh-CN')
}

const startEdit = () => {
  editForm.name = currentUser.value.name
  editForm.age = currentUser.value.age
  editForm.bio = currentUser.value.bio || ''
  editForm.interests = [...currentUser.value.interests]
  editForm.tags = [...(currentUser.value.tags || [])]
  editForm.socialLinks = { ...(currentUser.value.socialLinks || {}) }
  isEditing.value = true
}

const cancelEdit = () => {
  isEditing.value = false
  newTag.value = ''
}

// 标签管理
const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !editForm.tags.includes(tag) && editForm.tags.length < 5) {
    editForm.tags.push(tag)
    newTag.value = ''
  }
}

const addSuggestedTag = (tag) => {
  if (!editForm.tags.includes(tag) && editForm.tags.length < 5) {
    editForm.tags.push(tag)
  }
}

const removeTag = (tag) => {
  const index = editForm.tags.indexOf(tag)
  if (index > -1) {
    editForm.tags.splice(index, 1)
  }
}

// 在线状态切换
const toggleOnlineStatus = () => {
  const newStatus = !currentUser.value.isOnline
  currentUser.value.isOnline = newStatus
  currentUser.value.lastActive = new Date().toISOString()

  LocalStorageManager.updateUser(currentUser.value.id, {
    isOnline: newStatus,
    lastActive: currentUser.value.lastActive
  })

  LocalStorageManager.setCurrentUser(currentUser.value)
}

const saveProfile = async () => {
  if (editForm.interests.length < 3) {
    alert('请至少选择3个兴趣爱好')
    return
  }

  isSaving.value = true

  try {
    // 清理社交链接，移除空值
    const cleanedSocialLinks = {}
    Object.keys(editForm.socialLinks).forEach(platform => {
      if (editForm.socialLinks[platform] && editForm.socialLinks[platform].trim()) {
        cleanedSocialLinks[platform] = editForm.socialLinks[platform].trim()
      }
    })

    const result = LocalStorageManager.updateUser(currentUser.value.id, {
      name: editForm.name.trim(),
      age: editForm.age,
      bio: editForm.bio.trim(),
      interests: editForm.interests,
      tags: editForm.tags,
      socialLinks: cleanedSocialLinks,
      lastActive: new Date().toISOString()
    })

    if (result.success) {
      currentUser.value = result.data
      LocalStorageManager.setCurrentUser(result.data)
      isEditing.value = false
      newTag.value = ''

      // 重新获取匹配结果
      getTopMatches()
    } else {
      alert('保存失败：' + result.message)
    }
  } catch (error) {
    console.error('保存资料失败:', error)
    alert('保存失败，请重试')
  } finally {
    isSaving.value = false
  }
}

// 头像相关
const selectAvatar = (avatarUrl) => {
  const result = LocalStorageManager.updateUser(currentUser.value.id, {
    avatar: avatarUrl
  })

  if (result.success) {
    currentUser.value = result.data
    LocalStorageManager.setCurrentUser(result.data)
    showAvatarUpload.value = false
  }
}

const handleAvatarUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      selectAvatar(e.target.result)
    }
    reader.readAsDataURL(file)
  }
}

// 隐私设置
const savePrivacySettings = () => {
  // 这里可以保存到本地存储或发送到服务器
  console.log('保存隐私设置:', privacySettings)
  showPrivacySettings.value = false
  alert('隐私设置已保存')
}

// 访客相关
const refreshVisitors = () => {
  if (currentUser.value) {
    recentVisitors.value = LocalStorageManager.getVisitorLogs(currentUser.value.id, 12)
  }
}

const viewVisitorProfile = (visitor) => {
  console.log('查看访客资料:', visitor.name)
  // 这里可以跳转到访客的资料页面
}

const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    LocalStorageManager.clearCurrentUser()
    router.push('/')
  }
}

const getTopMatches = () => {
  if (!currentUser.value) return
  
  const matches = LocalStorageManager.getMatches(currentUser.value, 3)
  topMatches.value = matches
}

// 组件挂载时初始化
onMounted(() => {
  currentUser.value = LocalStorageManager.getCurrentUser()

  if (!currentUser.value) {
    router.push('/register')
    return
  }

  // 确保用户有新增的字段
  if (!currentUser.value.socialLinks) {
    currentUser.value.socialLinks = {}
  }
  if (!currentUser.value.tags) {
    currentUser.value.tags = []
  }
  if (!currentUser.value.lastActive) {
    currentUser.value.lastActive = currentUser.value.createdAt
  }
  if (currentUser.value.isOnline === undefined) {
    currentUser.value.isOnline = true
  }

  // 更新用户数据
  LocalStorageManager.updateUser(currentUser.value.id, {
    socialLinks: currentUser.value.socialLinks,
    tags: currentUser.value.tags,
    lastActive: currentUser.value.lastActive,
    isOnline: currentUser.value.isOnline
  })

  getTopMatches()
  refreshVisitors()
})
</script>
