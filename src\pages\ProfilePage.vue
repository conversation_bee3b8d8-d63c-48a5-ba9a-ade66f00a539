<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-100 py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              👤 我的资料
            </span>
          </h1>
        </div>
        
        <button 
          class="btn btn-outline btn-sm"
          @click="logout"
        >
          退出登录
        </button>
      </div>

      <div v-if="currentUser" class="max-w-4xl mx-auto">
        <!-- 个人信息卡片 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
          <div class="card-body">
            <div class="flex flex-col lg:flex-row lg:items-center space-y-6 lg:space-y-0 lg:space-x-8">
              <!-- 头像 -->
              <div class="flex-shrink-0 text-center">
                <div class="avatar">
                  <div class="w-32 h-32 rounded-full ring ring-purple-200 ring-offset-4">
                    <img :src="currentUser.avatar" :alt="currentUser.name" />
                  </div>
                </div>
                <button class="btn btn-ghost btn-sm mt-4">
                  📷 更换头像
                </button>
              </div>

              <!-- 基本信息 -->
              <div class="flex-1 space-y-4">
                <div>
                  <h2 class="text-3xl font-bold text-gray-800">{{ currentUser.name }}</h2>
                  <div class="flex flex-wrap items-center gap-3 mt-2">
                    <span 
                      class="badge text-white text-lg px-4 py-3"
                      :class="getMBTIColor(currentUser.mbtiType)"
                    >
                      {{ currentUser.mbtiType }} - {{ getMBTIName(currentUser.mbtiType) }}
                    </span>
                    <span class="badge badge-outline badge-lg">{{ currentUser.age }}岁</span>
                    <span class="badge badge-outline badge-lg">{{ currentUser.gender }}</span>
                  </div>
                </div>

                <div v-if="currentUser.bio">
                  <h3 class="font-semibold text-gray-700 mb-2">自我介绍</h3>
                  <p class="text-gray-600">{{ currentUser.bio }}</p>
                </div>

                <div>
                  <h3 class="font-semibold text-gray-700 mb-2">兴趣爱好</h3>
                  <div class="flex flex-wrap gap-2">
                    <span 
                      v-for="interest in currentUser.interests" 
                      :key="interest"
                      class="badge badge-primary text-white"
                    >
                      {{ interest }}
                    </span>
                  </div>
                </div>

                <div class="text-sm text-gray-500">
                  加入时间：{{ formatDate(currentUser.createdAt) }}
                </div>
              </div>

              <!-- 编辑按钮 -->
              <div class="flex-shrink-0">
                <button 
                  class="btn btn-primary"
                  @click="isEditing = true"
                >
                  ✏️ 编辑资料
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
            <div class="card-body text-center">
              <div class="text-3xl text-purple-600 mb-2">{{ matchCount }}</div>
              <div class="text-sm text-gray-600">潜在匹配</div>
            </div>
          </div>
          
          <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
            <div class="card-body text-center">
              <div class="text-3xl text-pink-600 mb-2">{{ averageMatchScore }}%</div>
              <div class="text-sm text-gray-600">平均匹配度</div>
            </div>
          </div>
          
          <div class="card bg-white/70 backdrop-blur-sm shadow-lg">
            <div class="card-body text-center">
              <div class="text-3xl text-indigo-600 mb-2">{{ currentUser.interests.length }}</div>
              <div class="text-sm text-gray-600">兴趣爱好</div>
            </div>
          </div>
        </div>

        <!-- 最佳匹配预览 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-xl font-bold text-gray-800">💝 你的最佳匹配</h3>
              <button 
                class="btn btn-outline btn-sm"
                @click="$router.push('/matches')"
              >
                查看全部
              </button>
            </div>

            <div v-if="topMatches.length > 0" class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div 
                v-for="match in topMatches" 
                :key="match.id"
                class="card bg-gradient-to-br from-purple-50 to-pink-50 shadow-md hover:shadow-lg transition-all duration-300"
              >
                <div class="card-body p-4">
                  <div class="flex items-center space-x-3 mb-3">
                    <div class="avatar">
                      <div class="w-12 h-12 rounded-full">
                        <img :src="match.avatar" :alt="match.name" />
                      </div>
                    </div>
                    <div>
                      <h4 class="font-bold text-sm">{{ match.name }}</h4>
                      <span class="text-xs text-gray-600">{{ match.mbtiType }}</span>
                    </div>
                  </div>
                  
                  <div class="text-center">
                    <div 
                      class="radial-progress text-primary text-sm"
                      :style="`--value:${match.matchScore}; --size:3rem; --thickness: 4px;`"
                    >
                      {{ match.matchScore }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-8">
              <div class="text-4xl mb-4">😔</div>
              <p class="text-gray-600">暂无匹配结果，等待更多用户加入</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑模态框 -->
      <div v-if="isEditing" class="modal modal-open">
        <div class="modal-box max-w-2xl">
          <h3 class="font-bold text-lg mb-4">编辑个人资料</h3>
          
          <form @submit.prevent="saveProfile" class="space-y-4">
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">姓名</span>
                </label>
                <input 
                  type="text" 
                  v-model="editForm.name"
                  class="input input-bordered"
                  required
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">年龄</span>
                </label>
                <input 
                  type="number" 
                  v-model.number="editForm.age"
                  class="input input-bordered"
                  min="16"
                  max="100"
                  required
                />
              </div>
            </div>

            <!-- 自我介绍 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">自我介绍</span>
                <span class="label-text-alt">{{ editForm.bio.length }}/200</span>
              </label>
              <textarea 
                v-model="editForm.bio"
                class="textarea textarea-bordered h-24"
                maxlength="200"
              ></textarea>
            </div>

            <!-- 兴趣爱好 -->
            <div class="form-control">
              <label class="label">
                <span class="label-text">兴趣爱好</span>
                <span class="label-text-alt">至少选择 3 个</span>
              </label>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-2 max-h-48 overflow-y-auto">
                <label 
                  v-for="interest in COMMON_INTERESTS" 
                  :key="interest"
                  class="label cursor-pointer justify-start p-2 rounded hover:bg-gray-50"
                >
                  <input 
                    type="checkbox" 
                    :value="interest"
                    v-model="editForm.interests"
                    class="checkbox checkbox-primary checkbox-sm" 
                  />
                  <span class="label-text ml-2 text-sm">{{ interest }}</span>
                </label>
              </div>
            </div>

            <div class="modal-action">
              <button 
                type="button"
                class="btn btn-ghost"
                @click="cancelEdit"
              >
                取消
              </button>
              <button 
                type="submit"
                class="btn btn-primary"
                :disabled="isSaving"
              >
                <span v-if="isSaving" class="loading loading-spinner loading-sm"></span>
                {{ isSaving ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 无用户状态 -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">😔</div>
        <h3 class="text-2xl font-bold text-gray-600 mb-4">未找到用户信息</h3>
        <p class="text-gray-500 mb-6">请先注册创建你的档案</p>
        <button 
          class="btn btn-primary"
          @click="$router.push('/register')"
        >
          立即注册
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import LocalStorageManager from '../utils/localStorage.js'
import { getMBTIColor, getMBTIName, COMMON_INTERESTS } from '../utils/mbtiTypes.js'

const router = useRouter()

// 响应式数据
const currentUser = ref(null)
const isEditing = ref(false)
const isSaving = ref(false)
const topMatches = ref([])

// 编辑表单
const editForm = reactive({
  name: '',
  age: null,
  bio: '',
  interests: []
})

// 计算属性
const matchCount = computed(() => {
  if (!currentUser.value) return 0
  const matches = LocalStorageManager.getMatches(currentUser.value, 10)
  return matches.length
})

const averageMatchScore = computed(() => {
  if (!currentUser.value) return 0
  const matches = LocalStorageManager.getMatches(currentUser.value, 10)
  if (matches.length === 0) return 0
  
  const total = matches.reduce((sum, match) => sum + match.matchScore, 0)
  return Math.round(total / matches.length)
})

// 方法
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const startEdit = () => {
  editForm.name = currentUser.value.name
  editForm.age = currentUser.value.age
  editForm.bio = currentUser.value.bio || ''
  editForm.interests = [...currentUser.value.interests]
  isEditing.value = true
}

const cancelEdit = () => {
  isEditing.value = false
}

const saveProfile = async () => {
  if (editForm.interests.length < 3) {
    alert('请至少选择3个兴趣爱好')
    return
  }

  isSaving.value = true

  try {
    const result = LocalStorageManager.updateUser(currentUser.value.id, {
      name: editForm.name.trim(),
      age: editForm.age,
      bio: editForm.bio.trim(),
      interests: editForm.interests
    })

    if (result.success) {
      currentUser.value = result.data
      LocalStorageManager.setCurrentUser(result.data)
      isEditing.value = false
      
      // 重新获取匹配结果
      getTopMatches()
    } else {
      alert('保存失败：' + result.message)
    }
  } catch (error) {
    console.error('保存资料失败:', error)
    alert('保存失败，请重试')
  } finally {
    isSaving.value = false
  }
}

const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    LocalStorageManager.clearCurrentUser()
    router.push('/')
  }
}

const getTopMatches = () => {
  if (!currentUser.value) return
  
  const matches = LocalStorageManager.getMatches(currentUser.value, 3)
  topMatches.value = matches
}

// 组件挂载时初始化
onMounted(() => {
  currentUser.value = LocalStorageManager.getCurrentUser()
  
  if (!currentUser.value) {
    router.push('/register')
    return
  }
  
  getTopMatches()
})
</script>
