<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-100 py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              ☁️ CloudBase 云开发演示
            </span>
          </h1>
        </div>
        
        <div class="w-20"></div>
      </div>

      <!-- 环境信息 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
        <div class="card-body">
          <h2 class="card-title text-xl mb-4">🌐 环境信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <strong>环境 ID:</strong> 
              <code class="bg-gray-100 px-2 py-1 rounded">cloud1-9ggccfb40bd93153</code>
            </div>
            <div>
              <strong>登录状态:</strong> 
              <span :class="loginStatus ? 'text-green-600' : 'text-red-600'">
                {{ loginStatus ? '✅ 已登录' : '❌ 未登录' }}
              </span>
            </div>
          </div>
          
          <div class="mt-4">
            <button 
              class="btn btn-primary btn-sm"
              @click="initCloudBase"
              :disabled="isLoading"
            >
              <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
              {{ isLoading ? '初始化中...' : '初始化 CloudBase' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 云函数测试 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 用户管理测试 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h3 class="card-title text-lg mb-4">👤 用户管理测试</h3>
            
            <div class="space-y-4">
              <button 
                class="btn btn-outline btn-sm w-full"
                @click="testCreateUser"
                :disabled="isLoading"
              >
                创建测试用户
              </button>
              
              <button 
                class="btn btn-outline btn-sm w-full"
                @click="testGetAllUsers"
                :disabled="isLoading"
              >
                获取所有用户
              </button>
              
              <div v-if="userTestResult" class="bg-gray-50 p-3 rounded text-sm">
                <strong>结果:</strong>
                <pre class="mt-2 whitespace-pre-wrap">{{ JSON.stringify(userTestResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- MBTI 匹配测试 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h3 class="card-title text-lg mb-4">💝 MBTI 匹配测试</h3>
            
            <div class="space-y-4">
              <button 
                class="btn btn-outline btn-sm w-full"
                @click="testGetMatches"
                :disabled="isLoading"
              >
                获取匹配列表
              </button>
              
              <button 
                class="btn btn-outline btn-sm w-full"
                @click="testCalculateMatch"
                :disabled="isLoading"
              >
                计算匹配度
              </button>
              
              <div v-if="matchTestResult" class="bg-gray-50 p-3 rounded text-sm">
                <strong>结果:</strong>
                <pre class="mt-2 whitespace-pre-wrap">{{ JSON.stringify(matchTestResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- 社交互动测试 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h3 class="card-title text-lg mb-4">💬 社交互动测试</h3>
            
            <div class="space-y-4">
              <button 
                class="btn btn-outline btn-sm w-full"
                @click="testSendMessage"
                :disabled="isLoading"
              >
                发送测试消息
              </button>
              
              <button 
                class="btn btn-outline btn-sm w-full"
                @click="testGetConversations"
                :disabled="isLoading"
              >
                获取对话列表
              </button>
              
              <div v-if="socialTestResult" class="bg-gray-50 p-3 rounded text-sm">
                <strong>结果:</strong>
                <pre class="mt-2 whitespace-pre-wrap">{{ JSON.stringify(socialTestResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据统计测试 -->
        <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
          <div class="card-body">
            <h3 class="card-title text-lg mb-4">📊 数据统计测试</h3>
            
            <div class="space-y-4">
              <button 
                class="btn btn-outline btn-sm w-full"
                @click="testGetStatistics"
                :disabled="isLoading"
              >
                获取统计数据
              </button>
              
              <button 
                class="btn btn-outline btn-sm w-full"
                @click="testGetMBTIDistribution"
                :disabled="isLoading"
              >
                获取 MBTI 分布
              </button>
              
              <div v-if="statsTestResult" class="bg-gray-50 p-3 rounded text-sm">
                <strong>结果:</strong>
                <pre class="mt-2 whitespace-pre-wrap">{{ JSON.stringify(statsTestResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mt-8">
        <div class="card-body">
          <div class="flex justify-between items-center mb-4">
            <h3 class="card-title text-lg">📝 操作日志</h3>
            <button 
              class="btn btn-ghost btn-sm"
              @click="clearLogs"
            >
              清空日志
            </button>
          </div>
          
          <div class="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-64 overflow-y-auto">
            <div v-for="(log, index) in logs" :key="index" class="mb-1">
              <span class="text-gray-500">[{{ formatTime(log.time) }}]</span>
              <span :class="log.type === 'error' ? 'text-red-400' : 'text-green-400'">
                {{ log.message }}
              </span>
            </div>
            <div v-if="logs.length === 0" class="text-gray-500">
              暂无日志记录...
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import cloudbaseUtils from '../utils/cloudbase.js'

const router = useRouter()

// 响应式数据
const loginStatus = ref(false)
const isLoading = ref(false)
const userTestResult = ref(null)
const matchTestResult = ref(null)
const socialTestResult = ref(null)
const statsTestResult = ref(null)
const logs = ref([])

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.push({
    time: new Date(),
    message,
    type
  })
}

// 格式化时间
const formatTime = (time) => {
  return time.toLocaleTimeString('zh-CN')
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 初始化 CloudBase
const initCloudBase = async () => {
  isLoading.value = true
  addLog('开始初始化 CloudBase...')
  
  try {
    await cloudbaseUtils.ensureLogin()
    loginStatus.value = true
    addLog('CloudBase 初始化成功！', 'success')
  } catch (error) {
    addLog(`CloudBase 初始化失败: ${error.message}`, 'error')
  } finally {
    isLoading.value = false
  }
}

// 测试创建用户
const testCreateUser = async () => {
  isLoading.value = true
  addLog('测试创建用户...')
  
  try {
    const userData = {
      name: `测试用户${Date.now()}`,
      age: 25,
      gender: '男',
      mbtiType: 'ENFP',
      interests: ['音乐', '旅行', '编程'],
      bio: '这是一个通过云函数创建的测试用户',
      tags: ['技术达人', '音乐爱好者']
    }
    
    const result = await cloudbaseUtils.userManagement('createUser', { userData })
    userTestResult.value = result
    addLog('用户创建成功！', 'success')
  } catch (error) {
    addLog(`用户创建失败: ${error.message}`, 'error')
    userTestResult.value = { error: error.message }
  } finally {
    isLoading.value = false
  }
}

// 测试获取所有用户
const testGetAllUsers = async () => {
  isLoading.value = true
  addLog('测试获取所有用户...')
  
  try {
    const result = await cloudbaseUtils.userManagement('getAllUsers', { limit: 5 })
    userTestResult.value = result
    addLog(`获取到 ${result.data.length} 个用户`, 'success')
  } catch (error) {
    addLog(`获取用户失败: ${error.message}`, 'error')
    userTestResult.value = { error: error.message }
  } finally {
    isLoading.value = false
  }
}

// 测试获取匹配
const testGetMatches = async () => {
  isLoading.value = true
  addLog('测试获取匹配列表...')
  
  try {
    // 使用第一个测试用户的 ID
    const userId = 'dd1eb2b4687508f30550736f7494d03d'
    const result = await cloudbaseUtils.mbtiMatch('getMatches', { userId, limit: 3 })
    matchTestResult.value = result
    addLog(`获取到 ${result.data.length} 个匹配`, 'success')
  } catch (error) {
    addLog(`获取匹配失败: ${error.message}`, 'error')
    matchTestResult.value = { error: error.message }
  } finally {
    isLoading.value = false
  }
}

// 测试计算匹配度
const testCalculateMatch = async () => {
  isLoading.value = true
  addLog('测试计算匹配度...')
  
  try {
    const userId = 'dd1eb2b4687508f30550736f7494d03d'
    const targetUserId = '6c2530cc68750902054b203b175cdd43'
    const result = await cloudbaseUtils.mbtiMatch('calculateMatch', { userId, targetUserId })
    matchTestResult.value = result
    addLog(`匹配度计算完成: ${result.data.matchScore}%`, 'success')
  } catch (error) {
    addLog(`匹配度计算失败: ${error.message}`, 'error')
    matchTestResult.value = { error: error.message }
  } finally {
    isLoading.value = false
  }
}

// 测试发送消息
const testSendMessage = async () => {
  isLoading.value = true
  addLog('测试发送消息...')
  
  try {
    const userId = 'dd1eb2b4687508f30550736f7494d03d'
    const targetUserId = '6c2530cc68750902054b203b175cdd43'
    const messageContent = `这是一条通过云函数发送的测试消息 - ${new Date().toLocaleTimeString()}`
    
    const result = await cloudbaseUtils.socialInteraction('sendMessage', {
      userId,
      targetUserId,
      messageContent
    })
    socialTestResult.value = result
    addLog('消息发送成功！', 'success')
  } catch (error) {
    addLog(`消息发送失败: ${error.message}`, 'error')
    socialTestResult.value = { error: error.message }
  } finally {
    isLoading.value = false
  }
}

// 测试获取对话列表
const testGetConversations = async () => {
  isLoading.value = true
  addLog('测试获取对话列表...')
  
  try {
    const userId = 'dd1eb2b4687508f30550736f7494d03d'
    const result = await cloudbaseUtils.socialInteraction('getConversations', { userId })
    socialTestResult.value = result
    addLog(`获取到 ${result.data.length} 个对话`, 'success')
  } catch (error) {
    addLog(`获取对话失败: ${error.message}`, 'error')
    socialTestResult.value = { error: error.message }
  } finally {
    isLoading.value = false
  }
}

// 测试获取统计数据
const testGetStatistics = async () => {
  isLoading.value = true
  addLog('测试获取统计数据...')
  
  try {
    const result = await cloudbaseUtils.dataStatistics('getAllStatistics')
    statsTestResult.value = result
    addLog('统计数据获取成功！', 'success')
  } catch (error) {
    addLog(`统计数据获取失败: ${error.message}`, 'error')
    statsTestResult.value = { error: error.message }
  } finally {
    isLoading.value = false
  }
}

// 测试获取 MBTI 分布
const testGetMBTIDistribution = async () => {
  isLoading.value = true
  addLog('测试获取 MBTI 分布...')
  
  try {
    const result = await cloudbaseUtils.dataStatistics('getMBTIDistribution')
    statsTestResult.value = result
    addLog('MBTI 分布数据获取成功！', 'success')
  } catch (error) {
    addLog(`MBTI 分布获取失败: ${error.message}`, 'error')
    statsTestResult.value = { error: error.message }
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  addLog('CloudBase 演示页面已加载')
  addLog('点击"初始化 CloudBase"按钮开始测试')
})
</script>

<style scoped>
pre {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
