{"envId": "cloud1-9ggccfb40bd93153", "version": "2.0", "framework": {"name": "vue", "plugins": {"client": {"use": "@cloudbase/framework-plugin-website", "inputs": {"buildCommand": "npm run build", "outputPath": "dist", "cloudPath": "/mbti-friends"}}}}, "functions": [{"name": "user-management", "timeout": 30, "envVariables": {"NODE_ENV": "production"}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main", "ignore": ["node_modules/**/*", ".git/**/*"]}, {"name": "mbti-match", "timeout": 30, "envVariables": {"NODE_ENV": "production"}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main", "ignore": ["node_modules/**/*", ".git/**/*"]}, {"name": "social-interaction", "timeout": 30, "envVariables": {"NODE_ENV": "production"}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main", "ignore": ["node_modules/**/*", ".git/**/*"]}, {"name": "data-statistics", "timeout": 30, "envVariables": {"NODE_ENV": "production"}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main", "ignore": ["node_modules/**/*", ".git/**/*"]}], "databases": [{"collectionName": "users"}, {"collectionName": "follows"}, {"collectionName": "conversations"}, {"collectionName": "messages"}, {"collectionName": "visitors"}, {"collectionName": "match_history"}]}