// 本地存储管理工具
// 用于第一阶段开发，后续会切换到 CloudBase

const STORAGE_KEYS = {
  USERS: 'mbti_friends_users',
  CURRENT_USER: 'mbti_friends_current_user'
}

// MBTI 兼容性矩阵 - 基于心理学研究的匹配度
const MBTI_COMPATIBILITY = {
  'ENFP': { 'INTJ': 95, 'INFJ': 90, 'ENFJ': 85, 'ENTP': 80, 'ISFP': 75, 'ISTP': 70, 'ESFP': 65, 'ESTP': 60, 'INFP': 85, 'INTP': 75, 'ENTJ': 70, 'ESTJ': 50, 'ISFJ': 60, 'ISTJ': 45, 'ESFJ': 55, 'ENFP': 70 },
  'INTJ': { 'ENFP': 95, 'ENTP': 90, 'INFP': 85, 'INTP': 80, 'ENFJ': 75, 'ENTJ': 70, 'INFJ': 85, 'ISFP': 65, 'ISTP': 75, 'ESFP': 50, 'ESTP': 55, 'ESTJ': 60, 'ISFJ': 45, 'ISTJ': 70, 'ESFJ': 40, 'INTJ': 75 },
  'INFJ': { 'ENFP': 90, 'ENTP': 85, 'INFP': 80, 'INTJ': 85, 'ENFJ': 75, 'ENTJ': 70, 'ISFP': 70, 'ISTP': 65, 'ESFP': 55, 'ESTP': 50, 'ESTJ': 55, 'ISFJ': 70, 'ISTJ': 65, 'ESFJ': 60, 'INTP': 75, 'INFJ': 70 },
  'ENFJ': { 'INFP': 90, 'ISFP': 85, 'ENFP': 85, 'INFJ': 75, 'INTP': 80, 'INTJ': 75, 'ENTP': 70, 'ISTP': 65, 'ESFP': 75, 'ESTP': 70, 'ESTJ': 65, 'ISFJ': 80, 'ISTJ': 60, 'ESFJ': 75, 'ENTJ': 65, 'ENFJ': 70 },
  'ENTP': { 'INTJ': 90, 'INFJ': 85, 'ENFJ': 70, 'ENFP': 80, 'INTP': 85, 'INFP': 75, 'ISFP': 60, 'ISTP': 70, 'ESFP': 65, 'ESTP': 75, 'ENTJ': 80, 'ESTJ': 70, 'ISFJ': 55, 'ISTJ': 50, 'ESFJ': 60, 'ENTP': 75 },
  'INFP': { 'ENFJ': 90, 'ENTJ': 85, 'INTJ': 85, 'ENTP': 75, 'ENFP': 85, 'INFJ': 80, 'ISFP': 75, 'ISTP': 70, 'ESFP': 60, 'ESTP': 55, 'ESTJ': 50, 'ISFJ': 65, 'ISTJ': 55, 'ESFJ': 60, 'INTP': 70, 'INFP': 75 },
  'ISFP': { 'ENFJ': 85, 'ESFJ': 80, 'ENFP': 75, 'INFP': 75, 'ISFJ': 70, 'ESFP': 75, 'ESTP': 70, 'ESTJ': 65, 'INTJ': 65, 'INFJ': 70, 'ENTP': 60, 'INTP': 65, 'ENTJ': 60, 'ISTJ': 70, 'ISTP': 65, 'ISFP': 70 },
  'ISTP': { 'ESFJ': 85, 'ENFJ': 65, 'ESTP': 80, 'ESTJ': 75, 'ISFJ': 70, 'ESFP': 75, 'ENFP': 70, 'INFP': 70, 'INTJ': 75, 'INFJ': 65, 'ENTP': 70, 'INTP': 75, 'ENTJ': 70, 'ISTJ': 65, 'ISFP': 65, 'ISTP': 70 },
  'ESFP': { 'ISFJ': 85, 'ISTJ': 80, 'ESFJ': 75, 'ISFP': 75, 'ESTP': 70, 'ESTJ': 70, 'ENFJ': 75, 'INFJ': 55, 'ENFP': 65, 'INFP': 60, 'INTJ': 50, 'ENTP': 65, 'INTP': 55, 'ENTJ': 60, 'ISTP': 75, 'ESFP': 70 },
  'ESTP': { 'ISFJ': 80, 'ISTJ': 75, 'ESFJ': 70, 'ESTJ': 75, 'ESFP': 70, 'ISTP': 80, 'ENFJ': 70, 'INFJ': 50, 'ENFP': 60, 'INFP': 55, 'INTJ': 55, 'ENTP': 75, 'INTP': 60, 'ENTJ': 70, 'ISFP': 70, 'ESTP': 75 },
  'ENTJ': { 'INFP': 85, 'INTP': 80, 'ENTP': 80, 'INTJ': 70, 'ENFP': 70, 'INFJ': 70, 'ENFJ': 65, 'ISFP': 60, 'ISTP': 70, 'ESFP': 60, 'ESTP': 70, 'ESTJ': 75, 'ISFJ': 55, 'ISTJ': 65, 'ESFJ': 60, 'ENTJ': 70 },
  'ESTJ': { 'ISFP': 65, 'ISTP': 75, 'ISFJ': 80, 'ISTJ': 85, 'ESFP': 70, 'ESTP': 75, 'ESFJ': 75, 'ENFJ': 65, 'INFJ': 55, 'ENFP': 50, 'INFP': 50, 'INTJ': 60, 'ENTP': 70, 'INTP': 55, 'ENTJ': 75, 'ESTJ': 70 },
  'ISFJ': { 'ESFP': 85, 'ESTP': 80, 'ENFJ': 80, 'ESFJ': 75, 'ISFP': 70, 'ISTP': 70, 'ESTJ': 80, 'ISTJ': 75, 'ENFP': 60, 'INFP': 65, 'INTJ': 45, 'INFJ': 70, 'ENTP': 55, 'INTP': 60, 'ENTJ': 55, 'ISFJ': 70 },
  'ISTJ': { 'ESFP': 80, 'ESTP': 75, 'ESFJ': 80, 'ESTJ': 85, 'ISFP': 70, 'ISTP': 65, 'ISFJ': 75, 'ENFJ': 60, 'INFJ': 65, 'ENFP': 45, 'INFP': 55, 'INTJ': 70, 'ENTP': 50, 'INTP': 65, 'ENTJ': 65, 'ISTJ': 75 },
  'ESFJ': { 'ISFP': 80, 'ISTP': 85, 'ISFJ': 75, 'ISTJ': 80, 'ESFP': 75, 'ESTP': 70, 'ESTJ': 75, 'ENFJ': 75, 'INFJ': 60, 'ENFP': 55, 'INFP': 60, 'INTJ': 40, 'ENTP': 60, 'INTP': 50, 'ENTJ': 60, 'ESFJ': 70 },
  'INTP': { 'ENTJ': 80, 'ENFJ': 80, 'ENTP': 85, 'INTJ': 80, 'ENFP': 75, 'INFJ': 75, 'INFP': 70, 'ISFP': 65, 'ISTP': 75, 'ESFP': 55, 'ESTP': 60, 'ESTJ': 55, 'ISFJ': 60, 'ISTJ': 65, 'ESFJ': 50, 'INTP': 75 }
}

/**
 * 计算两个 MBTI 类型的匹配度
 * @param {string} type1 - 第一个 MBTI 类型
 * @param {string} type2 - 第二个 MBTI 类型
 * @returns {number} 匹配度 (0-100)
 */
function calculateMBTICompatibility(type1, type2) {
  if (!MBTI_COMPATIBILITY[type1] || !MBTI_COMPATIBILITY[type1][type2]) {
    return 50 // 默认匹配度
  }
  return MBTI_COMPATIBILITY[type1][type2]
}

/**
 * 计算综合匹配度（包含年龄、兴趣等因素）
 * @param {Object} user1 - 用户1信息
 * @param {Object} user2 - 用户2信息
 * @returns {Object} 匹配结果 { score: number, reasons: string[] }
 */
function calculateOverallCompatibility(user1, user2) {
  // MBTI 匹配度权重 70%
  const mbtiScore = calculateMBTICompatibility(user1.mbtiType, user2.mbtiType)
  const mbtiWeightedScore = mbtiScore * 0.7
  
  // 年龄匹配度权重 15%
  const ageDiff = Math.abs(user1.age - user2.age)
  const ageScore = Math.max(0, 100 - ageDiff * 5)
  const ageWeightedScore = ageScore * 0.15
  
  // 兴趣匹配度权重 15%
  const commonInterests = user1.interests.filter(interest => 
    user2.interests.includes(interest)
  )
  const totalInterests = new Set([...user1.interests, ...user2.interests]).size
  const interestScore = totalInterests > 0 ? (commonInterests.length / totalInterests * 100) : 0
  const interestWeightedScore = interestScore * 0.15
  
  const totalScore = Math.round(mbtiWeightedScore + ageWeightedScore + interestWeightedScore)
  
  // 生成匹配原因
  const reasons = []
  if (mbtiScore >= 80) {
    reasons.push(`MBTI 类型高度匹配 (${mbtiScore}%)`)
  } else if (mbtiScore >= 60) {
    reasons.push(`MBTI 类型较为匹配 (${mbtiScore}%)`)
  }
  
  if (ageDiff <= 3) {
    reasons.push('年龄相近')
  } else if (ageDiff <= 5) {
    reasons.push('年龄差距适中')
  }
  
  if (commonInterests.length > 0) {
    reasons.push(`共同兴趣: ${commonInterests.join(', ')}`)
  }
  
  return {
    score: totalScore,
    reasons: reasons.length > 0 ? reasons : ['基础匹配']
  }
}

// 本地存储操作类
export class LocalStorageManager {
  /**
   * 添加用户
   * @param {Object} userData - 用户数据
   * @returns {Object} 添加结果
   */
  static addUser(userData) {
    try {
      const users = this.getAllUsers()
      const newUser = {
        id: Date.now().toString(),
        ...userData,
        createdAt: new Date().toISOString()
      }
      
      users.push(newUser)
      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users))
      
      return {
        success: true,
        data: newUser,
        message: '用户添加成功'
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message
      }
    }
  }

  /**
   * 获取所有用户
   * @returns {Array} 用户列表
   */
  static getAllUsers() {
    try {
      const users = localStorage.getItem(STORAGE_KEYS.USERS)
      return users ? JSON.parse(users) : []
    } catch (error) {
      console.error('获取用户列表失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取用户
   * @param {string} id - 用户ID
   * @returns {Object|null} 用户信息
   */
  static getUserById(id) {
    const users = this.getAllUsers()
    return users.find(user => user.id === id) || null
  }

  /**
   * 更新用户信息
   * @param {string} id - 用户ID
   * @param {Object} updateData - 更新数据
   * @returns {Object} 更新结果
   */
  static updateUser(id, updateData) {
    try {
      const users = this.getAllUsers()
      const userIndex = users.findIndex(user => user.id === id)
      
      if (userIndex === -1) {
        return {
          success: false,
          data: null,
          message: '用户不存在'
        }
      }
      
      users[userIndex] = { ...users[userIndex], ...updateData }
      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users))
      
      return {
        success: true,
        data: users[userIndex],
        message: '用户更新成功'
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message
      }
    }
  }

  /**
   * 删除用户
   * @param {string} id - 用户ID
   * @returns {Object} 删除结果
   */
  static deleteUser(id) {
    try {
      const users = this.getAllUsers()
      const filteredUsers = users.filter(user => user.id !== id)
      
      if (users.length === filteredUsers.length) {
        return {
          success: false,
          data: null,
          message: '用户不存在'
        }
      }
      
      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(filteredUsers))
      
      return {
        success: true,
        data: null,
        message: '用户删除成功'
      }
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message
      }
    }
  }

  /**
   * 获取匹配用户
   * @param {Object} currentUser - 当前用户信息
   * @param {number} limit - 返回数量限制，默认3个
   * @returns {Array} 匹配用户列表
   */
  static getMatches(currentUser, limit = 3) {
    const allUsers = this.getAllUsers()
    const otherUsers = allUsers.filter(user => user.id !== currentUser.id)
    
    if (otherUsers.length === 0) {
      return []
    }
    
    // 计算每个用户的匹配度
    const matches = otherUsers.map(user => {
      const compatibility = calculateOverallCompatibility(currentUser, user)
      return {
        ...user,
        matchScore: compatibility.score,
        matchReasons: compatibility.reasons
      }
    })
    
    // 按匹配度排序并返回前N个
    return matches
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, limit)
  }

  /**
   * 设置当前用户
   * @param {Object} user - 用户信息
   */
  static setCurrentUser(user) {
    localStorage.setItem(STORAGE_KEYS.CURRENT_USER, JSON.stringify(user))
  }

  /**
   * 获取当前用户
   * @returns {Object|null} 当前用户信息
   */
  static getCurrentUser() {
    try {
      const user = localStorage.getItem(STORAGE_KEYS.CURRENT_USER)
      return user ? JSON.parse(user) : null
    } catch (error) {
      console.error('获取当前用户失败:', error)
      return null
    }
  }

  /**
   * 清除当前用户
   */
  static clearCurrentUser() {
    localStorage.removeItem(STORAGE_KEYS.CURRENT_USER)
  }

  /**
   * 初始化示例数据
   */
  static initSampleData() {
    const existingUsers = this.getAllUsers()
    if (existingUsers.length > 0) {
      return // 已有数据，不需要初始化
    }

    const sampleUsers = [
      {
        name: '小明',
        age: 25,
        gender: '男',
        mbtiType: 'INTJ',
        interests: ['编程', '阅读', '音乐'],
        bio: '喜欢思考和创造，热爱技术和艺术的结合。',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
      },
      {
        name: '小红',
        age: 23,
        gender: '女',
        mbtiType: 'ENFP',
        interests: ['旅行', '摄影', '音乐', '美食'],
        bio: '热爱生活，喜欢探索新事物，相信每一天都充满可能。',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
      },
      {
        name: '小李',
        age: 27,
        gender: '男',
        mbtiType: 'INFJ',
        interests: ['写作', '心理学', '电影'],
        bio: '内向但富有洞察力，喜欢深度交流和思考人生。',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
      },
      {
        name: '小张',
        age: 24,
        gender: '女',
        mbtiType: 'ENFJ',
        interests: ['教育', '志愿服务', '瑜伽'],
        bio: '关心他人，希望能够帮助更多人成长和进步。',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
      },
      {
        name: '小王',
        age: 26,
        gender: '男',
        mbtiType: 'ENTP',
        interests: ['创业', '辩论', '科技'],
        bio: '充满创意和活力，喜欢挑战传统思维。',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face'
      }
    ]

    sampleUsers.forEach(userData => {
      this.addUser(userData)
    })
  }
}

export default LocalStorageManager
