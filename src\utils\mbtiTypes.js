// MBTI 类型定义和相关工具函数

export const MBTI_TYPES = [
  {
    type: 'ENFP',
    name: '竞选者',
    description: '热情、创造性和社交性强，总是能找到理由微笑。',
    traits: ['外向', '直觉', '情感', '感知'],
    color: 'bg-gradient-to-br from-pink-400 to-purple-500'
  },
  {
    type: 'INTJ',
    name: '建筑师',
    description: '富有想象力和战略性的思想家，一切皆在计划之中。',
    traits: ['内向', '直觉', '思考', '判断'],
    color: 'bg-gradient-to-br from-indigo-500 to-purple-600'
  },
  {
    type: 'INFJ',
    name: '提倡者',
    description: '安静而神秘，同时鼓舞他人且不知疲倦的理想主义者。',
    traits: ['内向', '直觉', '情感', '判断'],
    color: 'bg-gradient-to-br from-green-400 to-blue-500'
  },
  {
    type: 'ENFJ',
    name: '主人公',
    description: '富有魅力和鼓舞人心的领导者，能够使听众着迷。',
    traits: ['外向', '直觉', '情感', '判断'],
    color: 'bg-gradient-to-br from-yellow-400 to-orange-500'
  },
  {
    type: 'ENTP',
    name: '辩论家',
    description: '聪明好奇的思想家，不能抗拒智力上的挑战。',
    traits: ['外向', '直觉', '思考', '感知'],
    color: 'bg-gradient-to-br from-red-400 to-pink-500'
  },
  {
    type: 'INFP',
    name: '调停者',
    description: '诗意、善良和利他主义，总是热切地想要帮助好的事业。',
    traits: ['内向', '直觉', '情感', '感知'],
    color: 'bg-gradient-to-br from-teal-400 to-green-500'
  },
  {
    type: 'ISFP',
    name: '探险家',
    description: '灵活、迷人的艺术家，时刻准备探索新的可能性。',
    traits: ['内向', '感觉', '情感', '感知'],
    color: 'bg-gradient-to-br from-purple-400 to-pink-500'
  },
  {
    type: 'ISTP',
    name: '鉴赏家',
    description: '大胆而实际的实验家，擅长使用各种工具。',
    traits: ['内向', '感觉', '思考', '感知'],
    color: 'bg-gradient-to-br from-gray-500 to-blue-600'
  },
  {
    type: 'ESFP',
    name: '娱乐家',
    description: '自发的、精力充沛和热情的人，生活在他们周围从不无聊。',
    traits: ['外向', '感觉', '情感', '感知'],
    color: 'bg-gradient-to-br from-orange-400 to-red-500'
  },
  {
    type: 'ESTP',
    name: '企业家',
    description: '聪明、精力充沛和非常敏锐，真正享受生活在边缘。',
    traits: ['外向', '感觉', '思考', '感知'],
    color: 'bg-gradient-to-br from-yellow-500 to-red-500'
  },
  {
    type: 'ENTJ',
    name: '指挥官',
    description: '大胆、富有想象力、意志强烈的领导者，总是找到或创造解决方法。',
    traits: ['外向', '直觉', '思考', '判断'],
    color: 'bg-gradient-to-br from-blue-500 to-indigo-600'
  },
  {
    type: 'ESTJ',
    name: '总经理',
    description: '出色的管理者，在管理事情或人的时候无与伦比。',
    traits: ['外向', '感觉', '思考', '判断'],
    color: 'bg-gradient-to-br from-green-500 to-teal-600'
  },
  {
    type: 'ISFJ',
    name: '守护者',
    description: '非常专注和温暖的守护者，时刻准备保护爱的人。',
    traits: ['内向', '感觉', '情感', '判断'],
    color: 'bg-gradient-to-br from-blue-400 to-cyan-500'
  },
  {
    type: 'ISTJ',
    name: '物流师',
    description: '实际和注重事实的人，可靠性不容怀疑。',
    traits: ['内向', '感觉', '思考', '判断'],
    color: 'bg-gradient-to-br from-slate-500 to-gray-600'
  },
  {
    type: 'ESFJ',
    name: '执政官',
    description: '非常关心他人、社交和受欢迎，总是热切地想要帮助。',
    traits: ['外向', '感觉', '情感', '判断'],
    color: 'bg-gradient-to-br from-rose-400 to-pink-500'
  },
  {
    type: 'INTP',
    name: '逻辑学家',
    description: '创新的发明家，对知识有着止不住的渴望。',
    traits: ['内向', '直觉', '思考', '感知'],
    color: 'bg-gradient-to-br from-violet-500 to-purple-600'
  }
]

/**
 * 根据 MBTI 类型获取详细信息
 * @param {string} type - MBTI 类型
 * @returns {Object|null} MBTI 类型信息
 */
export function getMBTIInfo(type) {
  return MBTI_TYPES.find(mbti => mbti.type === type) || null
}

/**
 * 获取 MBTI 类型的颜色类名
 * @param {string} type - MBTI 类型
 * @returns {string} CSS 类名
 */
export function getMBTIColor(type) {
  const info = getMBTIInfo(type)
  return info ? info.color : 'bg-gradient-to-br from-gray-400 to-gray-500'
}

/**
 * 获取 MBTI 类型的名称
 * @param {string} type - MBTI 类型
 * @returns {string} 类型名称
 */
export function getMBTIName(type) {
  const info = getMBTIInfo(type)
  return info ? info.name : '未知类型'
}

/**
 * 获取 MBTI 类型的描述
 * @param {string} type - MBTI 类型
 * @returns {string} 类型描述
 */
export function getMBTIDescription(type) {
  const info = getMBTIInfo(type)
  return info ? info.description : '暂无描述'
}

/**
 * 验证 MBTI 类型是否有效
 * @param {string} type - MBTI 类型
 * @returns {boolean} 是否有效
 */
export function isValidMBTIType(type) {
  return MBTI_TYPES.some(mbti => mbti.type === type)
}

/**
 * 获取所有 MBTI 类型的简单列表
 * @returns {Array} 类型列表
 */
export function getAllMBTITypes() {
  return MBTI_TYPES.map(mbti => mbti.type)
}

/**
 * 根据特征分组 MBTI 类型
 * @returns {Object} 分组后的类型
 */
export function groupMBTIByCategory() {
  return {
    analysts: MBTI_TYPES.filter(mbti => ['INTJ', 'INTP', 'ENTJ', 'ENTP'].includes(mbti.type)),
    diplomats: MBTI_TYPES.filter(mbti => ['INFJ', 'INFP', 'ENFJ', 'ENFP'].includes(mbti.type)),
    sentinels: MBTI_TYPES.filter(mbti => ['ISTJ', 'ISFJ', 'ESTJ', 'ESFJ'].includes(mbti.type)),
    explorers: MBTI_TYPES.filter(mbti => ['ISTP', 'ISFP', 'ESTP', 'ESFP'].includes(mbti.type))
  }
}

// 常用兴趣爱好选项
export const COMMON_INTERESTS = [
  '阅读', '音乐', '电影', '旅行', '摄影', '运动', '游戏', '编程',
  '绘画', '写作', '烹饪', '园艺', '瑜伽', '健身', '舞蹈', '唱歌',
  '乐器演奏', '手工制作', '收藏', '钓鱼', '登山', '游泳', '跑步', '骑行',
  '美食', '咖啡', '茶艺', '红酒', '时尚', '美妆', '购物', '装修',
  '心理学', '哲学', '历史', '科学', '技术', '创业', '投资', '志愿服务',
  '宠物', '动漫', '漫画', '小说', '诗歌', '戏剧', '相声', '脱口秀'
]

/**
 * 获取推荐的兴趣爱好（基于 MBTI 类型）
 * @param {string} mbtiType - MBTI 类型
 * @returns {Array} 推荐的兴趣爱好
 */
export function getRecommendedInterests(mbtiType) {
  const recommendations = {
    'ENFP': ['旅行', '音乐', '写作', '摄影', '志愿服务', '戏剧'],
    'INTJ': ['阅读', '编程', '投资', '科学', '哲学', '收藏'],
    'INFJ': ['写作', '心理学', '音乐', '瑜伽', '志愿服务', '艺术'],
    'ENFJ': ['教育', '志愿服务', '音乐', '舞蹈', '心理学', '旅行'],
    'ENTP': ['创业', '辩论', '科技', '旅行', '音乐', '游戏'],
    'INFP': ['写作', '艺术', '音乐', '摄影', '志愿服务', '自然'],
    'ISFP': ['艺术', '音乐', '摄影', '手工制作', '园艺', '动物'],
    'ISTP': ['运动', '手工制作', '技术', '汽车', '户外活动', '游戏'],
    'ESFP': ['音乐', '舞蹈', '旅行', '美食', '时尚', '社交'],
    'ESTP': ['运动', '旅行', '冒险', '社交', '游戏', '美食'],
    'ENTJ': ['创业', '投资', '领导力', '战略', '技术', '高尔夫'],
    'ESTJ': ['管理', '运动', '投资', '传统活动', '社区服务', '收藏'],
    'ISFJ': ['烹饪', '园艺', '志愿服务', '家庭活动', '手工制作', '阅读'],
    'ISTJ': ['阅读', '收藏', '历史', '传统手工艺', '园艺', '理财'],
    'ESFJ': ['社交', '烹饪', '志愿服务', '时尚', '家庭活动', '音乐'],
    'INTP': ['编程', '科学', '哲学', '游戏', '数学', '理论研究']
  }
  
  return recommendations[mbtiType] || COMMON_INTERESTS.slice(0, 6)
}

export default {
  MBTI_TYPES,
  COMMON_INTERESTS,
  getMBTIInfo,
  getMBTIColor,
  getMBTIName,
  getMBTIDescription,
  isValidMBTIType,
  getAllMBTITypes,
  groupMBTIByCategory,
  getRecommendedInterests
}
