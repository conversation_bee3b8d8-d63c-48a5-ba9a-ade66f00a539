# MBTI - Friends 💝

基于 MBTI 人格类型的智能匹配平台，帮助用户找到最契合的朋友。使用 Vue 3、Vite、Tailwind CSS 和 DaisyUI 构建，支持本地存储和 CloudBase 云开发。

[![Powered by CloudBase](https://7463-tcb-advanced-a656fc-1257967285.tcb.qcloud.la/mcp/powered-by-cloudbase-badge.svg)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit)

> 本项目基于 [**CloudBase AI ToolKit**](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit) 开发，通过AI提示词和 MCP 协议+云开发，让开发更智能、更高效。

## ✨ 项目特点

- 🧠 **智能匹配算法** - 基于 MBTI 类型、年龄、兴趣爱好的科学匹配
- 💫 **个性化推荐** - 为每位用户推荐最合适的 3 位潜在朋友
- 🎨 **现代化设计** - 使用 Tailwind CSS 和 DaisyUI 的美观界面
- 📱 **响应式布局** - 完美支持移动端和桌面端
- 🚀 **极速开发体验** - Vite 构建工具提供热更新
- ⚡ **Vue 3 Composition API** - 现代化的 Vue.js 开发体验
- 🛣️ **Hash 路由** - 适合静态网站托管的路由方案
- 🎁 **CloudBase 集成** - 支持云数据库、云函数等后端服务

## 🎯 核心功能

### 用户管理
- **个人档案创建** - 输入姓名、年龄、性别、MBTI 类型、兴趣爱好
- **资料编辑** - 随时更新个人信息和兴趣爱好
- **头像展示** - 美观的用户头像显示

### MBTI 匹配系统
- **16种人格类型支持** - 完整的 MBTI 类型系统
- **科学匹配算法** - 基于心理学研究的兼容性矩阵
- **多维度评分** - 综合考虑 MBTI、年龄、兴趣等因素
- **匹配原因说明** - 详细解释为什么推荐这些用户

### 用户界面
- **首页展示** - 项目介绍和快速开始
- **用户注册** - 简洁的表单设计，支持实时验证
- **匹配结果** - 卡片式展示，包含匹配度和原因
- **用户列表** - 浏览所有用户，支持筛选和搜索
- **个人中心** - 查看和编辑个人资料

## 🛠️ 技术栈

### 前端技术
- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 6.x
- **路由管理**: Vue Router 4 (Hash 模式)
- **样式框架**: Tailwind CSS + DaisyUI
- **开发语言**: JavaScript ES6+

### 数据存储
- **第一阶段**: LocalStorage (本地存储)
- **第二阶段**: CloudBase (云数据库、云函数)

### 云开发资源 (CloudBase)
- **云数据库**: 存储用户信息和匹配数据
- **云函数**: MBTI 匹配算法和业务逻辑
- **身份认证**: 用户登录和身份验证
- **静态网站托管**: 前端应用部署

## 📁 项目结构

```
├── src/
│   ├── components/       # 可复用组件
│   │   └── Footer.vue   # 页脚组件
│   ├── pages/           # 页面组件
│   │   ├── HomePage.vue     # 首页
│   │   ├── RegisterPage.vue # 注册页面
│   │   ├── MatchesPage.vue  # 匹配结果页面
│   │   ├── UsersPage.vue    # 用户列表页面
│   │   └── ProfilePage.vue  # 个人资料页面
│   ├── utils/           # 工具函数
│   │   ├── cloudbase.js     # CloudBase 配置
│   │   ├── localStorage.js  # 本地存储管理
│   │   └── mbtiTypes.js     # MBTI 类型定义
│   ├── App.vue          # 应用入口组件
│   ├── main.js          # 应用入口文件
│   └── style.css        # 全局样式
├── cloudfunctions/      # 云函数目录
│   └── hello/          # 示例云函数
├── public/             # 静态资源
├── index.html          # HTML 模板
├── tailwind.config.js  # Tailwind 配置
├── vite.config.js      # Vite 配置
├── cloudbaserc.json    # CloudBase CLI 配置
└── package.json        # 项目依赖
```

## 🚀 快速开始

### 前提条件

- 安装 Node.js (版本 16 或更高)
- (可选) 腾讯云开发账号 - 用于云端部署

### 1. 克隆项目

```bash
git clone https://github.com/your-username/mbti-friends.git
cd mbti-friends
```

### 2. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

访问 `http://localhost:5173` 即可查看应用。

### 3. 体验功能

1. **注册用户** - 点击"开始匹配"创建你的 MBTI 档案
2. **查看匹配** - 系统会自动推荐最适合的朋友
3. **浏览用户** - 查看所有注册用户并筛选
4. **编辑资料** - 在个人中心更新你的信息

### 4. 构建生产版本

```bash
npm run build
```

构建产物将生成在 `dist` 目录中。

## 🚀 云端部署

### 快速部署到 CloudBase

1. **配置环境**
   ```bash
   # 复制配置模板
   cp cloudbaserc.template.json cloudbaserc.json
   cp .env.template .env.local

   # 编辑配置文件，填入您的 CloudBase 环境 ID
   ```

2. **部署应用**
   ```bash
   # 构建项目
   npm run build

   # 部署到云端
   tcb hosting:deploy dist
   ```

3. **访问应用**
   - 默认域名：`https://YOUR_ENV_ID.tcloudbaseapp.com`
   - 自定义路径：`https://YOUR_ENV_ID.tcloudbaseapp.com/mbti-friends`

### 详细部署指南

完整的部署说明请参考：[DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)

### 安全注意事项

⚠️ **重要**：在提交代码到 GitHub 前，请确保：
- `cloudbaserc.json` 已添加到 `.gitignore`
- 不要提交包含真实环境 ID 的配置文件
- 使用 `cloudbaserc.template.json` 作为配置模板

## 🧠 MBTI 匹配算法

### 匹配原理

本项目基于心理学研究实现了科学的 MBTI 匹配算法：

1. **MBTI 兼容性矩阵** (权重 70%)
   - 基于 16 种人格类型的兼容性研究
   - 例如：ENFP 与 INTJ 高度匹配 (95%)

2. **年龄匹配度** (权重 15%)
   - 年龄差距越小，匹配度越高
   - 每相差 1 岁，匹配度减少 5%

3. **兴趣匹配度** (权重 15%)
   - 共同兴趣爱好的比例
   - 共同兴趣越多，匹配度越高

### 匹配示例

```javascript
// 用户A: ENFP, 25岁, 兴趣[音乐, 旅行, 摄影]
// 用户B: INTJ, 27岁, 兴趣[音乐, 编程, 摄影]

// MBTI匹配: 95% × 0.7 = 66.5%
// 年龄匹配: (100 - 2×5) × 0.15 = 13.5%
// 兴趣匹配: (2/4) × 100 × 0.15 = 7.5%
// 总匹配度: 87.5%
```

## 🚀 部署指南

### 本地预览

```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
# 或使用 live-server
cd dist && npx live-server
```

### 部署到 CloudBase 静态托管

1. **构建项目**
   ```bash
   npm run build
   ```

2. **配置云开发环境**
   - 修改 `src/utils/cloudbase.js` 中的环境 ID
   - 部署云函数到 CloudBase

3. **上传到静态托管**
   ```bash
   # 使用 CloudBase CLI
   npm install -g @cloudbase/cli
   tcb login
   tcb hosting deploy dist -e your-env-id
   ```

4. **访问应用**
   - 获取静态托管域名
   - 配置自定义域名（可选）

## 💡 开发指南

### 数据存储切换

项目支持两种数据存储方式：

**第一阶段：本地存储 (当前)**
```javascript
import LocalStorageManager from '../utils/localStorage.js'

// 添加用户
const result = LocalStorageManager.addUser(userData)

// 获取匹配
const matches = LocalStorageManager.getMatches(currentUser, 3)
```

**第二阶段：CloudBase (待集成)**
```javascript
import { app } from '../utils/cloudbase.js'

// 数据库操作
const db = app.database()
await db.collection('users').add(userData)

// 调用云函数
const result = await app.callFunction({
  name: 'mbtiMatch',
  data: { action: 'getMatches', currentUser }
})
```

### 路由系统

项目使用 Hash Router 模式，适合静态网站托管：

```javascript
const routes = [
  { path: '/', component: HomePage },           // 首页
  { path: '/register', component: RegisterPage }, // 注册页面
  { path: '/matches', component: MatchesPage },   // 匹配结果
  { path: '/users', component: UsersPage },       // 用户列表
  { path: '/profile', component: ProfilePage },   // 个人资料
  { path: '/:pathMatch(.*)*', redirect: '/' }     // 404重定向
]
```

### 添加新功能

1. **创建新页面**
   ```bash
   # 在 src/pages/ 目录下创建新组件
   touch src/pages/NewPage.vue
   ```

2. **添加路由**
   ```javascript
   // 在 src/main.js 中添加路由
   import NewPage from './pages/NewPage.vue'
   const routes = [
     // ... 其他路由
     { path: '/new', component: NewPage }
   ]
   ```

3. **导航到新页面**
   ```vue
   <router-link to="/new" class="btn btn-primary">
     前往新页面
   </router-link>
   ```

## 🎨 UI 组件示例

### MBTI 类型卡片

```vue
<template>
  <div
    class="p-3 rounded-lg border-2 cursor-pointer transition-all duration-200"
    :class="[
      selected ? 'border-purple-500 bg-purple-50' : 'border-gray-200 bg-white',
      mbtiColor
    ]"
  >
    <div class="text-center">
      <div class="font-bold text-white text-sm mb-1">{{ mbtiType }}</div>
      <div class="text-xs text-white opacity-90">{{ mbtiName }}</div>
    </div>
  </div>
</template>
```

### 匹配度显示

```vue
<template>
  <div
    class="radial-progress text-primary text-4xl font-bold"
    :style="`--value:${matchScore}; --size:6rem; --thickness: 8px;`"
  >
    {{ matchScore }}%
  </div>
</template>
```

## 📊 数据结构

### 用户数据模型

```javascript
{
  id: "1640995200000",           // 用户ID (时间戳)
  name: "张三",                  // 姓名
  age: 25,                      // 年龄
  gender: "男",                 // 性别
  mbtiType: "ENFP",            // MBTI类型
  interests: [                  // 兴趣爱好
    "音乐", "旅行", "摄影"
  ],
  bio: "热爱生活的创意工作者",    // 自我介绍
  avatar: "https://...",        // 头像URL
  createdAt: "2023-12-01T..."   // 创建时间
}
```

### 匹配结果模型

```javascript
{
  ...userData,                  // 用户基本信息
  matchScore: 87,              // 匹配度 (0-100)
  matchReasons: [              // 匹配原因
    "MBTI 类型高度匹配 (95%)",
    "年龄相近",
    "共同兴趣: 音乐, 摄影"
  ]
}
```

## 🔧 配置说明

### Tailwind CSS 配置

```javascript
// tailwind.config.js
module.exports = {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#8b5cf6',    // 紫色主题
        secondary: '#ec4899'   // 粉色辅助
      }
    }
  },
  plugins: [require('daisyui')]
}
```

### Vite 配置

```javascript
// vite.config.js
export default defineConfig({
  plugins: [vue()],
  base: './',  // 相对路径，适合静态托管
  build: {
    outDir: 'dist',
    assetsDir: 'assets'
  }
})
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎯 完整的 MBTI 匹配系统
- 🎨 现代化 UI 设计
- 📱 响应式布局支持
- 💾 本地存储功能

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Tailwind CSS](https://tailwindcss.com/) - 原子化 CSS 框架
- [DaisyUI](https://daisyui.com/) - Tailwind CSS 组件库
- [CloudBase](https://tcb.cloud.tencent.com/) - 腾讯云开发平台
- [Unsplash](https://unsplash.com/) - 免费高质量图片