# MBTI Friends - 完整版社交匹配应用 💝

一个功能完整的基于 Vue 3 + Vite + Tailwind CSS + DaisyUI 构建的现代化 MBTI 社交匹配应用。

[![Powered by CloudBase](https://7463-tcb-advanced-a656fc-1257967285.tcb.qcloud.la/mcp/powered-by-cloudbase-badge.svg)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit)

> 本项目基于 [**CloudBase AI ToolKit**](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit) 开发，通过AI提示词和 MCP 协议+云开发，让开发更智能、更高效。

## 🌟 项目特色

- **智能匹配算法**：基于 MBTI 类型和兴趣爱好的科学匹配
- **完整社交功能**：消息系统、关注粉丝、访客记录
- **现代化界面**：多主题支持、响应式设计、无障碍优化
- **用户体验优化**：搜索建议、快捷键、主题切换
- **数据统计分析**：用户活跃度、匹配成功率、热门标签

## 🚀 快速开始

### 环境要求

- Node.js 16+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 📱 功能特性

### 🎯 核心功能
- **用户注册与认证**：创建个人 MBTI 档案
- **智能匹配系统**：基于 MBTI 类型的匹配算法
- **用户浏览与筛选**：高级筛选、排序、收藏功能
- **个人资料管理**：头像上传、社交链接、个人标签

### 💬 社交互动
- **消息系统**：实时聊天、对话管理
- **关注系统**：关注用户、粉丝管理、互相关注
- **访客记录**：查看谁访问了你的资料
- **互动历史**：完整的社交互动记录

### 📊 数据统计
- **用户统计**：总用户数、在线用户、新用户
- **MBTI 分布**：各类型用户分布情况
- **热门标签**：最受欢迎的个人标签
- **匹配成功率**：不同匹配度的成功率分析
- **活跃度统计**：用户活跃度趋势

### 🎨 用户体验
- **多主题支持**：浅色、深色、日落、海洋、森林主题
- **搜索建议**：智能搜索提示、历史记录
- **快捷键支持**：完整的键盘导航
- **无障碍优化**：屏幕阅读器支持、高对比度模式
- **响应式设计**：完美适配桌面端和移动端

### 🔧 高级功能
- **隐私设置**：个人资料可见性控制
- **主题切换**：跟随系统主题或手动选择
- **字体大小调节**：支持多种字体大小
- **减少动画模式**：适合对动画敏感的用户
- **键盘导航**：完整的无障碍支持

## 🛠️ 技术栈

- **前端框架**：Vue 3 (Composition API)
- **构建工具**：Vite 6.x
- **样式框架**：Tailwind CSS + DaisyUI
- **路由管理**：Vue Router 4
- **状态管理**：Composition API + localStorage
- **开发语言**：JavaScript (ES6+)
- **图标库**：Heroicons + Emoji

## 📁 项目结构

```
src/
├── components/              # 可复用组件
│   ├── Footer.vue          # 页脚组件
│   ├── SearchBox.vue       # 搜索框组件
│   ├── ThemeSwitcher.vue   # 主题切换器
│   ├── ShortcutHelp.vue    # 快捷键帮助
│   └── AccessibilitySettings.vue # 无障碍设置
├── pages/                   # 页面组件
│   ├── HomePage.vue         # 首页
│   ├── RegisterPage.vue     # 注册页面
│   ├── MatchesPage.vue      # 匹配页面
│   ├── UsersPage.vue        # 用户列表页面
│   ├── ProfilePage.vue      # 个人资料页面
│   ├── MessagesPage.vue     # 消息页面
│   ├── FollowsPage.vue      # 关注粉丝页面
│   └── StatsPage.vue        # 数据统计页面
├── utils/                   # 工具函数
│   ├── localStorage.js      # 本地存储管理
│   ├── mbtiTypes.js         # MBTI 类型定义
│   ├── theme.js             # 主题管理
│   └── accessibility.js    # 无障碍功能
├── main.js                  # 应用入口
└── style.css               # 全局样式
```

## 🎯 匹配算法

### 核心算法
1. **MBTI 类型兼容性**：基于心理学研究的类型匹配
2. **兴趣爱好相似度**：计算共同兴趣的重叠度
3. **年龄范围偏好**：考虑年龄差异的接受度
4. **个人标签匹配**：基于个人标签的相似性
5. **综合评分排序**：多维度加权计算最终匹配度

### 匹配等级
- **90%+ 完美匹配**：极高兼容性
- **80-89% 优秀匹配**：很好的兼容性
- **70-79% 良好匹配**：不错的兼容性
- **60-69% 一般匹配**：基础兼容性
- **60%以下 低匹配**：较低兼容性

## ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+K` | 快速搜索 |
| `Ctrl+H` | 返回首页 |
| `Ctrl+P` | 个人资料 |
| `Ctrl+M` | 消息中心 |
| `Ctrl+T` | 切换主题 |
| `?` | 显示帮助 |
| `Tab` | 下一个元素 |
| `Shift+Tab` | 上一个元素 |
| `Esc` | 关闭对话框 |

## 🌈 主题支持

- **浅色主题**：清新明亮，适合日间使用
- **深色主题**：护眼深色，适合夜间使用
- **日落主题**：温暖橙红，营造温馨氛围
- **海洋主题**：清凉蓝绿，带来宁静感受
- **森林主题**：自然绿色，回归大自然

## ♿ 无障碍支持

- **键盘导航**：完整的键盘操作支持
- **屏幕阅读器**：ARIA 标签和语义化标记
- **高对比度模式**：提高视觉可读性
- **字体大小调节**：支持多种字体大小
- **减少动画模式**：适合对动画敏感的用户
- **焦点管理**：清晰的焦点指示

## 📊 数据管理

### 本地存储结构
```javascript
// 用户数据
users: [
  {
    id: "unique-id",
    name: "用户名",
    age: 25,
    gender: "男/女/其他",
    mbtiType: "ENFP",
    interests: ["音乐", "旅行"],
    tags: ["技术达人", "音乐爱好者"],
    bio: "个人简介",
    avatar: "头像URL",
    socialLinks: { github: "链接" },
    isOnline: true,
    lastActive: "2024-01-01T00:00:00.000Z",
    createdAt: "2024-01-01T00:00:00.000Z"
  }
]

// 关注关系
follows: {
  "user-id": ["followed-user-id-1", "followed-user-id-2"]
}

// 访客记录
visitors: {
  "user-id": [
    {
      visitorId: "visitor-id",
      visitTime: "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

## 🎨 设计理念

- **用户体验优先**：简洁直观的界面设计
- **性能优化**：轻量级组件和高效渲染
- **可访问性**：完整的无障碍支持
- **响应式设计**：适配各种设备尺寸
- **渐进增强**：核心功能优先，逐步增强体验

## 🔮 未来规划

### 短期目标
- [ ] 云端数据同步（CloudBase 集成）
- [ ] 实时聊天功能
- [ ] 推送通知系统
- [ ] 更多匹配算法优化

### 中期目标
- [ ] 社交动态功能
- [ ] 群组聊天
- [ ] 活动组织功能
- [ ] 移动端 PWA

### 长期目标
- [ ] AI 智能推荐
- [ ] 视频通话功能
- [ ] 原生移动应用
- [ ] 多语言支持

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

- Vue.js 团队提供的优秀框架
- Tailwind CSS 和 DaisyUI 的美观组件
- MBTI 理论的心理学基础
- 开源社区的无私贡献

---

**Made with ❤️ by MBTI Friends Team**
