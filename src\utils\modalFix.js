// 模态框 z-index 修复工具

/**
 * 强制确保模态框显示在最前面
 */
export class ModalZIndexFixer {
  static MODAL_Z_INDEX = 99999
  static MODAL_BOX_Z_INDEX = 100000
  static NESTED_MODAL_Z_INDEX = 100001
  
  /**
   * 修复所有模态框的 z-index
   */
  static fixAllModals() {
    // 修复无障碍设置模态框
    this.fixAccessibilityModal()
    
    // 修复快捷键帮助模态框
    this.fixShortcutHelpModal()
    
    // 修复通用模态框
    this.fixGenericModals()
    
    // 修复下拉菜单
    this.fixDropdowns()
  }
  
  /**
   * 修复无障碍设置模态框
   */
  static fixAccessibilityModal() {
    const modal = document.querySelector('.accessibility-modal')
    if (modal) {
      this.applyModalStyles(modal, this.MODAL_Z_INDEX)
      
      const modalBox = modal.querySelector('.accessibility-modal-box')
      if (modalBox) {
        this.applyModalBoxStyles(modalBox, this.MODAL_BOX_Z_INDEX)
      }
    }
  }
  
  /**
   * 修复快捷键帮助模态框
   */
  static fixShortcutHelpModal() {
    const modal = document.querySelector('.shortcut-help-modal')
    if (modal) {
      this.applyModalStyles(modal, this.MODAL_Z_INDEX)
      
      const modalBox = modal.querySelector('.shortcut-help-modal-box')
      if (modalBox) {
        this.applyModalBoxStyles(modalBox, this.MODAL_BOX_Z_INDEX)
      }
    }
  }
  
  /**
   * 修复通用模态框
   */
  static fixGenericModals() {
    const modals = document.querySelectorAll('.modal.modal-open')
    modals.forEach((modal, index) => {
      const zIndex = this.MODAL_Z_INDEX + index
      this.applyModalStyles(modal, zIndex)
      
      const modalBox = modal.querySelector('.modal-box')
      if (modalBox) {
        this.applyModalBoxStyles(modalBox, zIndex + 1)
      }
    })
  }
  
  /**
   * 修复下拉菜单
   */
  static fixDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-content')
    dropdowns.forEach(dropdown => {
      dropdown.style.zIndex = '50000'
      dropdown.style.position = 'absolute'
    })
  }
  
  /**
   * 应用模态框样式
   */
  static applyModalStyles(modal, zIndex) {
    modal.style.zIndex = zIndex.toString()
    modal.style.position = 'fixed'
    modal.style.top = '0'
    modal.style.left = '0'
    modal.style.width = '100vw'
    modal.style.height = '100vh'
    modal.style.display = 'flex'
    modal.style.alignItems = 'center'
    modal.style.justifyContent = 'center'
  }
  
  /**
   * 应用模态框内容样式
   */
  static applyModalBoxStyles(modalBox, zIndex) {
    modalBox.style.zIndex = zIndex.toString()
    modalBox.style.position = 'relative'
  }
  
  /**
   * 监听 DOM 变化并自动修复
   */
  static startAutoFix() {
    // 立即修复一次
    this.fixAllModals()
    
    // 创建 MutationObserver 监听 DOM 变化
    const observer = new MutationObserver((mutations) => {
      let shouldFix = false
      
      mutations.forEach((mutation) => {
        // 检查是否有新的模态框被添加
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.classList?.contains('modal') || 
                  node.querySelector?.('.modal')) {
                shouldFix = true
              }
            }
          })
        }
        
        // 检查是否有 class 变化
        if (mutation.type === 'attributes' && 
            mutation.attributeName === 'class') {
          const target = mutation.target
          if (target.classList?.contains('modal-open')) {
            shouldFix = true
          }
        }
      })
      
      if (shouldFix) {
        // 延迟修复，确保 DOM 更新完成
        setTimeout(() => this.fixAllModals(), 10)
      }
    })
    
    // 开始观察
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class']
    })
    
    return observer
  }
  
  /**
   * 创建高优先级样式表
   */
  static injectHighPriorityStyles() {
    const styleId = 'modal-zindex-fix'
    
    // 如果已经存在，先移除
    const existingStyle = document.getElementById(styleId)
    if (existingStyle) {
      existingStyle.remove()
    }
    
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = `
      /* 超高优先级模态框样式 */
      .modal.modal-open,
      .accessibility-modal,
      .shortcut-help-modal {
        z-index: 99999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }
      
      .modal-box,
      .accessibility-modal-box,
      .shortcut-help-modal-box {
        z-index: 100000 !important;
        position: relative !important;
      }
      
      /* 下拉菜单修复 */
      .dropdown-content,
      .theme-switcher-dropdown {
        z-index: 50000 !important;
        position: absolute !important;
      }
      
      /* 确保模态框背景遮罩 */
      .modal::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: 99998 !important;
      }
    `
    
    // 插入到 head 的最后，确保最高优先级
    document.head.appendChild(style)
  }
  
  /**
   * 完整初始化
   */
  static init() {
    // 注入高优先级样式
    this.injectHighPriorityStyles()
    
    // 开始自动修复
    const observer = this.startAutoFix()
    
    // 页面加载完成后再次修复
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => this.fixAllModals(), 100)
      })
    } else {
      setTimeout(() => this.fixAllModals(), 100)
    }
    
    return observer
  }
}

// 自动初始化
if (typeof window !== 'undefined') {
  // 立即初始化
  ModalZIndexFixer.init()
  
  // 页面可见性变化时重新修复
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      setTimeout(() => ModalZIndexFixer.fixAllModals(), 50)
    }
  })
}

export default ModalZIndexFixer
