// 无障碍支持工具

/**
 * 无障碍管理器
 */
export class AccessibilityManager {
  static isHighContrastMode = false
  static isReducedMotionMode = false
  static fontSize = 'normal'

  /**
   * 初始化无障碍功能
   */
  static init() {
    this.detectSystemPreferences()
    this.loadUserPreferences()
    this.setupKeyboardNavigation()
    this.setupScreenReaderSupport()
    this.setupFocusManagement()
  }

  /**
   * 检测系统无障碍偏好
   */
  static detectSystemPreferences() {
    // 检测是否偏好减少动画
    if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.isReducedMotionMode = true
      this.applyReducedMotion()
    }

    // 检测是否偏好高对比度
    if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
      this.isHighContrastMode = true
      this.applyHighContrast()
    }

    // 监听系统偏好变化
    if (window.matchMedia) {
      window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
        this.isReducedMotionMode = e.matches
        this.applyReducedMotion()
      })

      window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
        this.isHighContrastMode = e.matches
        this.applyHighContrast()
      })
    }
  }

  /**
   * 加载用户无障碍偏好
   */
  static loadUserPreferences() {
    try {
      const preferences = localStorage.getItem('mbti_friends_accessibility')
      if (preferences) {
        const parsed = JSON.parse(preferences)
        this.isHighContrastMode = parsed.highContrast || false
        this.isReducedMotionMode = parsed.reducedMotion || false
        this.fontSize = parsed.fontSize || 'normal'
        
        this.applyPreferences()
      }
    } catch (error) {
      console.warn('加载无障碍偏好失败:', error)
    }
  }

  /**
   * 保存用户无障碍偏好
   */
  static saveUserPreferences() {
    try {
      const preferences = {
        highContrast: this.isHighContrastMode,
        reducedMotion: this.isReducedMotionMode,
        fontSize: this.fontSize
      }
      localStorage.setItem('mbti_friends_accessibility', JSON.stringify(preferences))
    } catch (error) {
      console.warn('保存无障碍偏好失败:', error)
    }
  }

  /**
   * 应用无障碍偏好
   */
  static applyPreferences() {
    this.applyHighContrast()
    this.applyReducedMotion()
    this.applyFontSize()
  }

  /**
   * 应用高对比度模式
   */
  static applyHighContrast() {
    const root = document.documentElement
    if (this.isHighContrastMode) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }
  }

  /**
   * 应用减少动画模式
   */
  static applyReducedMotion() {
    const root = document.documentElement
    if (this.isReducedMotionMode) {
      root.classList.add('reduced-motion')
    } else {
      root.classList.remove('reduced-motion')
    }
  }

  /**
   * 应用字体大小
   */
  static applyFontSize() {
    const root = document.documentElement
    root.classList.remove('font-small', 'font-normal', 'font-large', 'font-extra-large')
    root.classList.add(`font-${this.fontSize}`)
  }

  /**
   * 设置键盘导航
   */
  static setupKeyboardNavigation() {
    // 添加键盘导航样式
    const style = document.createElement('style')
    style.textContent = `
      /* 键盘焦点样式 */
      *:focus {
        outline: 2px solid #8b5cf6 !important;
        outline-offset: 2px !important;
      }
      
      /* 跳过链接 */
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #8b5cf6;
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
      }
      
      .skip-link:focus {
        top: 6px;
      }
      
      /* 高对比度模式 */
      .high-contrast {
        filter: contrast(150%);
      }
      
      .high-contrast .card {
        border: 2px solid #000 !important;
      }
      
      .high-contrast .btn {
        border: 2px solid #000 !important;
      }
      
      /* 减少动画模式 */
      .reduced-motion *,
      .reduced-motion *::before,
      .reduced-motion *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
      
      /* 字体大小 */
      .font-small { font-size: 14px; }
      .font-normal { font-size: 16px; }
      .font-large { font-size: 18px; }
      .font-extra-large { font-size: 20px; }
    `
    document.head.appendChild(style)

    // 添加跳过链接
    this.addSkipLinks()
  }

  /**
   * 添加跳过链接
   */
  static addSkipLinks() {
    const skipLink = document.createElement('a')
    skipLink.href = '#main-content'
    skipLink.className = 'skip-link'
    skipLink.textContent = '跳到主要内容'
    skipLink.setAttribute('aria-label', '跳过导航，直接到主要内容')
    
    document.body.insertBefore(skipLink, document.body.firstChild)
  }

  /**
   * 设置屏幕阅读器支持
   */
  static setupScreenReaderSupport() {
    // 添加 ARIA 标签
    this.addAriaLabels()
    
    // 添加实时区域用于动态内容通知
    this.createLiveRegion()
  }

  /**
   * 添加 ARIA 标签
   */
  static addAriaLabels() {
    // 为主要导航添加标签
    const nav = document.querySelector('nav')
    if (nav && !nav.getAttribute('aria-label')) {
      nav.setAttribute('aria-label', '主导航')
    }

    // 为主要内容区域添加标签
    const main = document.querySelector('main') || document.querySelector('#app')
    if (main && !main.getAttribute('role')) {
      main.setAttribute('role', 'main')
      main.setAttribute('id', 'main-content')
    }
  }

  /**
   * 创建实时区域
   */
  static createLiveRegion() {
    const liveRegion = document.createElement('div')
    liveRegion.id = 'live-region'
    liveRegion.setAttribute('aria-live', 'polite')
    liveRegion.setAttribute('aria-atomic', 'true')
    liveRegion.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `
    document.body.appendChild(liveRegion)
  }

  /**
   * 设置焦点管理
   */
  static setupFocusManagement() {
    // 模态框焦点陷阱
    this.setupModalFocusTrap()
    
    // 页面切换时的焦点管理
    this.setupPageFocusManagement()
  }

  /**
   * 设置模态框焦点陷阱
   */
  static setupModalFocusTrap() {
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        const modal = document.querySelector('.modal.modal-open')
        if (modal) {
          this.trapFocus(e, modal)
        }
      }
    })
  }

  /**
   * 焦点陷阱
   */
  static trapFocus(e, container) {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault()
        lastElement.focus()
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault()
        firstElement.focus()
      }
    }
  }

  /**
   * 设置页面焦点管理
   */
  static setupPageFocusManagement() {
    // 监听路由变化（如果使用 Vue Router）
    if (window.router) {
      window.router.afterEach(() => {
        this.focusMainContent()
      })
    }
  }

  /**
   * 焦点移到主要内容
   */
  static focusMainContent() {
    const mainContent = document.querySelector('#main-content') || document.querySelector('main')
    if (mainContent) {
      mainContent.setAttribute('tabindex', '-1')
      mainContent.focus()
      mainContent.removeAttribute('tabindex')
    }
  }

  /**
   * 通知屏幕阅读器
   */
  static announceToScreenReader(message) {
    const liveRegion = document.getElementById('live-region')
    if (liveRegion) {
      liveRegion.textContent = message
      setTimeout(() => {
        liveRegion.textContent = ''
      }, 1000)
    }
  }

  /**
   * 切换高对比度模式
   */
  static toggleHighContrast() {
    this.isHighContrastMode = !this.isHighContrastMode
    this.applyHighContrast()
    this.saveUserPreferences()
    
    this.announceToScreenReader(
      this.isHighContrastMode ? '已开启高对比度模式' : '已关闭高对比度模式'
    )
  }

  /**
   * 切换减少动画模式
   */
  static toggleReducedMotion() {
    this.isReducedMotionMode = !this.isReducedMotionMode
    this.applyReducedMotion()
    this.saveUserPreferences()
    
    this.announceToScreenReader(
      this.isReducedMotionMode ? '已开启减少动画模式' : '已关闭减少动画模式'
    )
  }

  /**
   * 设置字体大小
   */
  static setFontSize(size) {
    const validSizes = ['small', 'normal', 'large', 'extra-large']
    if (validSizes.includes(size)) {
      this.fontSize = size
      this.applyFontSize()
      this.saveUserPreferences()
      
      const sizeNames = {
        'small': '小',
        'normal': '正常',
        'large': '大',
        'extra-large': '特大'
      }
      
      this.announceToScreenReader(`字体大小已设置为${sizeNames[size]}`)
    }
  }

  /**
   * 获取当前设置
   */
  static getSettings() {
    return {
      highContrast: this.isHighContrastMode,
      reducedMotion: this.isReducedMotionMode,
      fontSize: this.fontSize
    }
  }
}

export default AccessibilityManager
