<template>
  <!-- 无障碍设置按钮 -->
  <button 
    class="btn btn-ghost btn-sm"
    @click="showSettings = true"
    title="无障碍设置"
    aria-label="打开无障碍设置"
  >
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
    </svg>
    <span class="hidden sm:inline ml-1">无障碍</span>
  </button>

  <!-- 无障碍设置模态框 -->
  <div v-if="showSettings" class="modal modal-open z-[9999]" role="dialog" aria-labelledby="accessibility-title">
    <div class="modal-box max-w-2xl">
      <div class="flex justify-between items-center mb-6">
        <h3 id="accessibility-title" class="font-bold text-xl">♿ 无障碍设置</h3>
        <button 
          class="btn btn-ghost btn-sm btn-circle"
          @click="showSettings = false"
          aria-label="关闭设置"
        >
          ✕
        </button>
      </div>

      <div class="space-y-6">
        <!-- 视觉设置 -->
        <div>
          <h4 class="font-semibold text-lg mb-4 text-primary">👁️ 视觉设置</h4>
          
          <!-- 高对比度模式 -->
          <div class="form-control">
            <label class="label cursor-pointer">
              <div>
                <span class="label-text font-medium">高对比度模式</span>
                <div class="label-text-alt text-gray-500">增强文字和背景的对比度，提高可读性</div>
              </div>
              <input 
                type="checkbox" 
                v-model="settings.highContrast"
                class="toggle toggle-primary" 
                @change="toggleHighContrast"
                aria-describedby="high-contrast-desc"
              />
            </label>
          </div>

          <!-- 字体大小 -->
          <div class="form-control mt-4">
            <label class="label">
              <span class="label-text font-medium">字体大小</span>
            </label>
            <div class="grid grid-cols-4 gap-2">
              <button 
                v-for="size in fontSizes" 
                :key="size.value"
                class="btn btn-sm"
                :class="settings.fontSize === size.value ? 'btn-primary' : 'btn-outline'"
                @click="setFontSize(size.value)"
                :aria-pressed="settings.fontSize === size.value"
              >
                {{ size.label }}
              </button>
            </div>
          </div>
        </div>

        <!-- 动画设置 -->
        <div>
          <h4 class="font-semibold text-lg mb-4 text-secondary">🎬 动画设置</h4>
          
          <!-- 减少动画 -->
          <div class="form-control">
            <label class="label cursor-pointer">
              <div>
                <span class="label-text font-medium">减少动画效果</span>
                <div class="label-text-alt text-gray-500">减少或禁用页面动画，适合对动画敏感的用户</div>
              </div>
              <input 
                type="checkbox" 
                v-model="settings.reducedMotion"
                class="toggle toggle-secondary" 
                @change="toggleReducedMotion"
                aria-describedby="reduced-motion-desc"
              />
            </label>
          </div>
        </div>

        <!-- 键盘导航 -->
        <div>
          <h4 class="font-semibold text-lg mb-4 text-accent">⌨️ 键盘导航</h4>
          
          <div class="bg-base-200 p-4 rounded-lg">
            <h5 class="font-medium mb-3">键盘快捷键</h5>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <div class="flex justify-between">
                <span>Tab / Shift+Tab</span>
                <span class="text-gray-500">切换焦点</span>
              </div>
              <div class="flex justify-between">
                <span>Enter / Space</span>
                <span class="text-gray-500">激活元素</span>
              </div>
              <div class="flex justify-between">
                <span>Escape</span>
                <span class="text-gray-500">关闭对话框</span>
              </div>
              <div class="flex justify-between">
                <span>Arrow Keys</span>
                <span class="text-gray-500">导航选项</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 屏幕阅读器 -->
        <div>
          <h4 class="font-semibold text-lg mb-4 text-info">🔊 屏幕阅读器</h4>
          
          <div class="alert alert-info">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
              <h3 class="font-bold">屏幕阅读器支持</h3>
              <div class="text-sm">
                <p>本网站已优化支持屏幕阅读器，包括：</p>
                <ul class="list-disc list-inside mt-2 space-y-1">
                  <li>完整的 ARIA 标签和语义化标记</li>
                  <li>键盘导航和焦点管理</li>
                  <li>动态内容的实时通知</li>
                  <li>跳过链接和地标导航</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试区域 -->
        <div>
          <h4 class="font-semibold text-lg mb-4 text-warning">🧪 测试区域</h4>
          
          <div class="space-y-3">
            <button 
              class="btn btn-outline btn-sm"
              @click="testScreenReader"
            >
              测试屏幕阅读器通知
            </button>
            
            <button 
              class="btn btn-outline btn-sm"
              @click="testKeyboardNavigation"
            >
              测试键盘导航
            </button>
            
            <button 
              class="btn btn-outline btn-sm"
              @click="resetSettings"
            >
              重置所有设置
            </button>
          </div>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="modal-action">
        <button 
          class="btn btn-ghost"
          @click="showSettings = false"
        >
          关闭
        </button>
        <button 
          class="btn btn-primary"
          @click="saveAndClose"
        >
          保存设置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import AccessibilityManager from '../utils/accessibility.js'

// 响应式数据
const showSettings = ref(false)
const settings = reactive({
  highContrast: false,
  reducedMotion: false,
  fontSize: 'normal'
})

// 字体大小选项
const fontSizes = [
  { value: 'small', label: '小' },
  { value: 'normal', label: '正常' },
  { value: 'large', label: '大' },
  { value: 'extra-large', label: '特大' }
]

// 方法
const loadSettings = () => {
  const currentSettings = AccessibilityManager.getSettings()
  Object.assign(settings, currentSettings)
}

const toggleHighContrast = () => {
  AccessibilityManager.toggleHighContrast()
}

const toggleReducedMotion = () => {
  AccessibilityManager.toggleReducedMotion()
}

const setFontSize = (size) => {
  settings.fontSize = size
  AccessibilityManager.setFontSize(size)
}

const testScreenReader = () => {
  AccessibilityManager.announceToScreenReader('这是一个屏幕阅读器测试消息。如果您听到这条消息，说明屏幕阅读器支持正常工作。')
}

const testKeyboardNavigation = () => {
  // 聚焦到第一个可聚焦元素
  const firstFocusable = document.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')
  if (firstFocusable) {
    firstFocusable.focus()
    AccessibilityManager.announceToScreenReader('键盘导航测试：焦点已移动到第一个可聚焦元素')
  }
}

const resetSettings = () => {
  if (confirm('确定要重置所有无障碍设置吗？')) {
    settings.highContrast = false
    settings.reducedMotion = false
    settings.fontSize = 'normal'
    
    AccessibilityManager.isHighContrastMode = false
    AccessibilityManager.isReducedMotionMode = false
    AccessibilityManager.fontSize = 'normal'
    
    AccessibilityManager.applyPreferences()
    AccessibilityManager.saveUserPreferences()
    AccessibilityManager.announceToScreenReader('无障碍设置已重置为默认值')
  }
}

const saveAndClose = () => {
  AccessibilityManager.saveUserPreferences()
  AccessibilityManager.announceToScreenReader('无障碍设置已保存')
  showSettings.value = false
}

// 生命周期
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
/* 确保模态框在高对比度模式下也能正常显示 */
.modal-box {
  border: 2px solid transparent;
}

.high-contrast .modal-box {
  border-color: #000;
}

/* 改善焦点可见性 */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
}

/* 高对比度模式下的特殊样式 */
.high-contrast .alert {
  border: 2px solid #000;
}

.high-contrast .form-control {
  border: 1px solid #666;
  border-radius: 4px;
  padding: 8px;
  margin: 4px 0;
}
</style>
