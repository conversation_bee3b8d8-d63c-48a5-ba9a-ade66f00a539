const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const db = app.database();

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  try {
    const { action, userId, targetUserId, messageContent, conversationId } = event;
    
    switch (action) {
      case 'followUser':
        // 关注用户
        const existingFollow = await db.collection('follows')
          .where({
            followerId: userId,
            followeeId: targetUserId
          })
          .get();
        
        if (existingFollow.data.length > 0) {
          return {
            success: false,
            message: '已经关注过该用户'
          };
        }
        
        await db.collection('follows').add({
          followerId: userId,
          followeeId: targetUserId,
          createdAt: new Date()
        });
        
        return {
          success: true,
          message: '关注成功'
        };
        
      case 'unfollowUser':
        // 取消关注
        const followRecord = await db.collection('follows')
          .where({
            followerId: userId,
            followeeId: targetUserId
          })
          .get();
        
        if (followRecord.data.length === 0) {
          return {
            success: false,
            message: '未关注该用户'
          };
        }
        
        await db.collection('follows').doc(followRecord.data[0]._id).remove();
        
        return {
          success: true,
          message: '取消关注成功'
        };
        
      case 'getFollowing':
        // 获取关注列表
        const followingResult = await db.collection('follows')
          .where({ followerId: userId })
          .get();
        
        const followingUsers = [];
        for (const follow of followingResult.data) {
          const userResult = await db.collection('users').doc(follow.followeeId).get();
          if (userResult.data.length > 0) {
            followingUsers.push({
              ...userResult.data[0],
              followTime: follow.createdAt
            });
          }
        }
        
        return {
          success: true,
          data: followingUsers
        };
        
      case 'getFollowers':
        // 获取粉丝列表
        const followersResult = await db.collection('follows')
          .where({ followeeId: userId })
          .get();
        
        const followers = [];
        for (const follow of followersResult.data) {
          const userResult = await db.collection('users').doc(follow.followerId).get();
          if (userResult.data.length > 0) {
            followers.push({
              ...userResult.data[0],
              followTime: follow.createdAt
            });
          }
        }
        
        return {
          success: true,
          data: followers
        };
        
      case 'sendMessage':
        // 发送消息
        if (!messageContent || messageContent.trim().length === 0) {
          return {
            success: false,
            message: '消息内容不能为空'
          };
        }
        
        // 创建或获取对话
        let conversation;
        if (conversationId) {
          const convResult = await db.collection('conversations').doc(conversationId).get();
          conversation = convResult.data[0];
        } else {
          // 查找现有对话
          const existingConv = await db.collection('conversations')
            .where({
              participants: db.command.all([userId, targetUserId])
            })
            .get();
          
          if (existingConv.data.length > 0) {
            conversation = existingConv.data[0];
          } else {
            // 创建新对话
            const newConvResult = await db.collection('conversations').add({
              participants: [userId, targetUserId],
              createdAt: new Date(),
              updatedAt: new Date()
            });
            conversation = { _id: newConvResult.id, participants: [userId, targetUserId] };
          }
        }
        
        // 添加消息
        const messageResult = await db.collection('messages').add({
          conversationId: conversation._id,
          senderId: userId,
          receiverId: targetUserId,
          content: messageContent.trim(),
          timestamp: new Date(),
          isRead: false
        });
        
        // 更新对话的最后消息时间
        await db.collection('conversations').doc(conversation._id).update({
          lastMessageTime: new Date(),
          lastMessage: messageContent.trim()
        });
        
        return {
          success: true,
          data: {
            messageId: messageResult.id,
            conversationId: conversation._id
          }
        };
        
      case 'getConversations':
        // 获取对话列表
        const conversationsResult = await db.collection('conversations')
          .where({
            participants: db.command.in([userId])
          })
          .orderBy('lastMessageTime', 'desc')
          .get();
        
        const conversations = [];
        for (const conv of conversationsResult.data) {
          const otherUserId = conv.participants.find(id => id !== userId);
          const otherUserResult = await db.collection('users').doc(otherUserId).get();
          
          if (otherUserResult.data.length > 0) {
            // 获取未读消息数
            const unreadCount = await db.collection('messages')
              .where({
                conversationId: conv._id,
                receiverId: userId,
                isRead: false
              })
              .count();
            
            conversations.push({
              id: conv._id,
              otherUser: otherUserResult.data[0],
              lastMessage: {
                content: conv.lastMessage || '',
                timestamp: conv.lastMessageTime || conv.createdAt
              },
              unreadCount: unreadCount.total
            });
          }
        }
        
        return {
          success: true,
          data: conversations
        };
        
      case 'getMessages':
        // 获取对话消息
        const { limit = 50, offset = 0 } = event;
        
        const messagesResult = await db.collection('messages')
          .where({ conversationId: conversationId })
          .orderBy('timestamp', 'desc')
          .skip(offset)
          .limit(limit)
          .get();
        
        // 标记消息为已读
        const unreadMessages = messagesResult.data.filter(msg => 
          msg.receiverId === userId && !msg.isRead
        );
        
        for (const msg of unreadMessages) {
          await db.collection('messages').doc(msg._id).update({ isRead: true });
        }
        
        return {
          success: true,
          data: messagesResult.data.reverse() // 按时间正序返回
        };
        
      case 'markAsRead':
        // 标记消息为已读
        await db.collection('messages')
          .where({
            conversationId: conversationId,
            receiverId: userId,
            isRead: false
          })
          .update({ isRead: true });
        
        return {
          success: true,
          message: '消息已标记为已读'
        };
        
      case 'deleteConversation':
        // 删除对话
        await db.collection('conversations').doc(conversationId).remove();
        await db.collection('messages')
          .where({ conversationId: conversationId })
          .remove();
        
        return {
          success: true,
          message: '对话已删除'
        };
        
      default:
        return {
          success: false,
          message: '不支持的操作'
        };
    }
    
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: error.message
    };
  }
};
