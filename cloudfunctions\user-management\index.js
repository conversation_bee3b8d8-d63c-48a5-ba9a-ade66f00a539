const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发
const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
});

const db = app.database();

/**
 * 验证用户数据
 */
function validateUserData(userData) {
  const errors = [];
  
  if (!userData.name || userData.name.trim().length < 2) {
    errors.push('姓名至少需要2个字符');
  }
  
  if (!userData.age || userData.age < 16 || userData.age > 100) {
    errors.push('年龄必须在16-100之间');
  }
  
  if (!userData.gender || !['男', '女', '其他'].includes(userData.gender)) {
    errors.push('性别必须是男、女或其他');
  }
  
  if (!userData.mbtiType || !/^[EI][NS][FT][JP]$/.test(userData.mbtiType)) {
    errors.push('MBTI类型格式不正确');
  }
  
  if (!userData.interests || !Array.isArray(userData.interests) || userData.interests.length < 3) {
    errors.push('至少需要选择3个兴趣爱好');
  }
  
  return errors;
}

/**
 * 生成用户头像
 */
function generateAvatar(name, gender) {
  const avatars = {
    '男': [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face'
    ],
    '女': [
      'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face'
    ],
    '其他': [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    ]
  };
  
  const genderAvatars = avatars[gender] || avatars['其他'];
  const hash = name.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
  return genderAvatars[hash % genderAvatars.length];
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  try {
    const { action, userId, userData, updateData } = event;
    
    switch (action) {
      case 'createUser':
        // 创建用户
        const validationErrors = validateUserData(userData);
        if (validationErrors.length > 0) {
          return {
            success: false,
            message: '数据验证失败',
            errors: validationErrors
          };
        }
        
        // 检查用户名是否已存在
        const existingUser = await db.collection('users')
          .where({ name: userData.name })
          .get();
        
        if (existingUser.data.length > 0) {
          return {
            success: false,
            message: '用户名已存在'
          };
        }
        
        // 创建新用户
        const newUser = {
          ...userData,
          avatar: userData.avatar || generateAvatar(userData.name, userData.gender),
          tags: userData.tags || [],
          socialLinks: userData.socialLinks || {},
          bio: userData.bio || '',
          isOnline: true,
          lastActive: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        const createResult = await db.collection('users').add(newUser);
        
        return {
          success: true,
          data: {
            _id: createResult.id,
            ...newUser
          }
        };
        
      case 'updateUser':
        // 更新用户信息
        if (updateData.name || updateData.age || updateData.gender || updateData.mbtiType || updateData.interests) {
          const validationErrors = validateUserData({ ...userData, ...updateData });
          if (validationErrors.length > 0) {
            return {
              success: false,
              message: '数据验证失败',
              errors: validationErrors
            };
          }
        }
        
        const updateResult = await db.collection('users').doc(userId).update({
          ...updateData,
          updatedAt: new Date()
        });
        
        // 获取更新后的用户信息
        const updatedUser = await db.collection('users').doc(userId).get();
        
        return {
          success: true,
          data: updatedUser.data[0]
        };
        
      case 'getUser':
        // 获取用户信息
        const userResult = await db.collection('users').doc(userId).get();
        
        if (!userResult.data.length) {
          return {
            success: false,
            message: '用户不存在'
          };
        }
        
        return {
          success: true,
          data: userResult.data[0]
        };
        
      case 'getAllUsers':
        // 获取所有用户列表
        const { limit = 50, offset = 0, filters = {} } = event;
        
        let query = db.collection('users');
        
        // 应用筛选条件
        if (filters.mbtiType) {
          query = query.where({ mbtiType: filters.mbtiType });
        }
        if (filters.gender) {
          query = query.where({ gender: filters.gender });
        }
        if (filters.ageMin || filters.ageMax) {
          const ageFilter = {};
          if (filters.ageMin) ageFilter[db.command.gte] = filters.ageMin;
          if (filters.ageMax) ageFilter[db.command.lte] = filters.ageMax;
          query = query.where({ age: ageFilter });
        }
        
        const allUsersResult = await query
          .orderBy('createdAt', 'desc')
          .skip(offset)
          .limit(limit)
          .get();
        
        return {
          success: true,
          data: allUsersResult.data,
          total: allUsersResult.data.length
        };
        
      case 'updateOnlineStatus':
        // 更新在线状态
        await db.collection('users').doc(userId).update({
          isOnline: event.isOnline,
          lastActive: new Date()
        });
        
        return {
          success: true,
          message: '在线状态已更新'
        };
        
      case 'recordVisitor':
        // 记录访客
        const { visitorId } = event;
        
        // 检查是否已经记录过（24小时内）
        const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const existingVisit = await db.collection('visitors')
          .where({
            userId: userId,
            visitorId: visitorId,
            visitTime: db.command.gte(yesterday)
          })
          .get();
        
        if (existingVisit.data.length === 0) {
          await db.collection('visitors').add({
            userId: userId,
            visitorId: visitorId,
            visitTime: new Date()
          });
        }
        
        return {
          success: true,
          message: '访客记录已更新'
        };
        
      case 'getVisitors':
        // 获取访客列表
        const visitorsResult = await db.collection('visitors')
          .where({ userId: userId })
          .orderBy('visitTime', 'desc')
          .limit(20)
          .get();
        
        // 获取访客用户信息
        const visitorIds = visitorsResult.data.map(v => v.visitorId);
        const visitorsInfo = [];
        
        for (const visitorId of visitorIds) {
          const visitorResult = await db.collection('users').doc(visitorId).get();
          if (visitorResult.data.length > 0) {
            const visitRecord = visitorsResult.data.find(v => v.visitorId === visitorId);
            visitorsInfo.push({
              visitor: visitorResult.data[0],
              visitTime: visitRecord.visitTime
            });
          }
        }
        
        return {
          success: true,
          data: visitorsInfo
        };
        
      default:
        return {
          success: false,
          message: '不支持的操作'
        };
    }
    
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: error.message
    };
  }
};
