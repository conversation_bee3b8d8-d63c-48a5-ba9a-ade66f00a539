# 📋 GitHub 提交前检查清单

在将代码提交到 GitHub 之前，请确保完成以下检查：

## 🔒 安全检查

### ✅ 敏感信息保护
- [ ] `cloudbaserc.json` 已添加到 `.gitignore`
- [ ] `.env.local` 已添加到 `.gitignore`
- [ ] 代码中没有硬编码的环境 ID
- [ ] 代码中没有硬编码的 API 密钥
- [ ] 代码中没有硬编码的数据库连接信息

### ✅ 配置文件检查
- [ ] `cloudbaserc.template.json` 已创建并使用占位符
- [ ] `.env.template` 已创建并使用占位符
- [ ] `DEPLOYMENT_GUIDE.md` 已创建
- [ ] `README.md` 已更新部署说明

## 📁 文件结构检查

### ✅ 必需文件
- [ ] `.gitignore` 文件存在且完整
- [ ] `package.json` 包含正确的脚本
- [ ] `README.md` 包含完整的项目说明
- [ ] `DEPLOYMENT_GUIDE.md` 包含详细的部署指南

### ✅ 模板文件
- [ ] `cloudbaserc.template.json` - CloudBase 配置模板
- [ ] `.env.template` - 环境变量模板
- [ ] `GITHUB_CHECKLIST.md` - 本检查清单

## 🧪 功能测试

### ✅ 本地测试
- [ ] `npm install` 成功执行
- [ ] `npm run dev` 成功启动开发服务器
- [ ] `npm run build` 成功构建生产版本
- [ ] 所有页面正常加载
- [ ] 主题切换功能正常
- [ ] 模态框显示正常（无障碍设置、帮助等）

### ✅ 功能验证
- [ ] 用户注册功能正常
- [ ] MBTI 匹配算法正常
- [ ] 搜索功能正常
- [ ] 无障碍功能正常
- [ ] 快捷键功能正常

## 📝 文档检查

### ✅ README.md
- [ ] 项目描述清晰
- [ ] 安装说明完整
- [ ] 功能特性列表完整
- [ ] 部署指南链接正确
- [ ] 技术栈信息准确

### ✅ 代码注释
- [ ] 关键函数有注释
- [ ] 复杂逻辑有说明
- [ ] API 接口有文档
- [ ] 组件有使用说明

## 🔧 代码质量

### ✅ 代码规范
- [ ] 没有 console.log 调试信息
- [ ] 没有 TODO 或 FIXME 注释
- [ ] 变量命名规范
- [ ] 函数命名清晰

### ✅ 性能优化
- [ ] 图片资源已优化
- [ ] 无用代码已删除
- [ ] 依赖包已清理
- [ ] 构建产物大小合理

## 🌐 部署准备

### ✅ CloudBase 配置
- [ ] 云函数代码完整
- [ ] 数据库集合定义清晰
- [ ] 环境变量配置正确
- [ ] 权限设置合理

### ✅ CI/CD 准备
- [ ] GitHub Actions 配置（如果需要）
- [ ] 部署脚本测试通过
- [ ] 环境变量在 GitHub Secrets 中配置

## 📊 最终检查

### ✅ 提交前验证
```bash
# 1. 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 2. 运行测试
npm run dev
npm run build

# 3. 检查构建产物
ls -la dist/

# 4. 验证 .gitignore
git status
# 确保敏感文件不在待提交列表中
```

### ✅ Git 提交
- [ ] 提交信息清晰描述变更
- [ ] 分支名称规范
- [ ] 没有提交敏感文件
- [ ] 代码已经过 review

## 🚨 紧急情况处理

### 如果意外提交了敏感信息：

1. **立即删除敏感信息**
   ```bash
   # 从最新提交中删除文件
   git rm --cached cloudbaserc.json
   git commit -m "Remove sensitive config file"
   ```

2. **重写历史记录**（如果已推送）
   ```bash
   # 谨慎使用，会重写 Git 历史
   git filter-branch --force --index-filter \
   'git rm --cached --ignore-unmatch cloudbaserc.json' \
   --prune-empty --tag-name-filter cat -- --all
   ```

3. **更换敏感信息**
   - 立即更换 CloudBase 环境密钥
   - 重新生成 API 密钥
   - 更新数据库访问权限

## ✅ 检查完成

当所有项目都勾选完成后，您就可以安全地将代码提交到 GitHub 了！

---

**记住：安全第一，永远不要提交敏感信息到公共仓库！** 🔒
