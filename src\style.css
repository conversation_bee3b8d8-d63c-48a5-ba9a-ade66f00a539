@tailwind base;
@tailwind components;
@tailwind utilities;

/* 主题相关样式 */
:root {
  --theme-primary: linear-gradient(to right, #9333ea, #ec4899);
  --theme-secondary: linear-gradient(to right, #3b82f6, #6366f1);
  --theme-accent: linear-gradient(to right, #10b981, #06b6d4);
  --theme-background: linear-gradient(to bottom right, #faf5ff, #fdf2f8, #eef2ff);
}

/* 主题背景类 */
.theme-light {
  background: linear-gradient(to bottom right, #faf5ff, #fdf2f8, #eef2ff);
}

.theme-dark {
  background: linear-gradient(to bottom right, #111827, #581c87, #312e81);
}

.theme-sunset {
  background: linear-gradient(to bottom right, #fed7aa, #fecaca, #fce7f3);
}

.theme-ocean {
  background: linear-gradient(to bottom right, #dbeafe, #cffafe, #a7f3d0);
}

.theme-forest {
  background: linear-gradient(to bottom right, #dcfce7, #d1fae5, #bbf7d0);
}

/* 确保模态框在最顶层 - 使用极高的 z-index */
.modal {
  z-index: 99999 !important;
}

.modal.modal-open {
  z-index: 99999 !important;
}

.modal-box {
  z-index: 100000 !important;
}

/* 特定模态框的超高优先级 */
.accessibility-modal,
.shortcut-help-modal {
  z-index: 99999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

.accessibility-modal-box,
.shortcut-help-modal-box {
  z-index: 100000 !important;
  position: relative !important;
}

/* 下拉菜单 z-index */
.dropdown-content {
  z-index: 9998 !important;
}

.dropdown.dropdown-open .dropdown-content {
  z-index: 9998 !important;
}

/* 确保导航栏下拉菜单正确显示 */
.navbar .dropdown-content {
  z-index: 9997 !important;
}

/* 覆盖所有可能的 z-index 冲突 */
.modal-backdrop {
  z-index: 99998 !important;
}

/* 确保模态框内容不被遮挡 */
.modal-open {
  overflow: hidden;
}

.modal-open .modal {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 主题渐变背景应用 */
.bg-theme-gradient {
  background: var(--theme-background);
}

.bg-theme-primary {
  background: var(--theme-primary);
}

.bg-theme-secondary {
  background: var(--theme-secondary);
}

.bg-theme-accent {
  background: var(--theme-accent);
}

/* 全局样式 */
html {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

body {
  margin: 0;
  padding: 0;
}

#app {
  min-height: 100vh;
} 