<template>
  <div class="min-h-screen bg-theme-gradient py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              💝 为你推荐的朋友
            </span>
          </h1>
        </div>
        
        <button 
          class="btn btn-outline btn-sm"
          @click="$router.push('/profile')"
        >
          我的资料
        </button>
      </div>

      <!-- 当前用户信息 -->
      <div v-if="currentUser" class="card bg-white/80 backdrop-blur-sm shadow-lg mb-8">
        <div class="card-body">
          <div class="flex items-center space-x-4">
            <div class="avatar">
              <div class="w-16 h-16 rounded-full">
                <img :src="currentUser.avatar" :alt="currentUser.name" />
              </div>
            </div>
            <div class="flex-1">
              <h3 class="text-xl font-bold">{{ currentUser.name }}</h3>
              <div class="flex items-center space-x-2 mt-1">
                <span 
                  class="badge text-white text-sm px-3 py-2"
                  :class="getMBTIColor(currentUser.mbtiType)"
                >
                  {{ currentUser.mbtiType }} - {{ getMBTIName(currentUser.mbtiType) }}
                </span>
                <span class="text-gray-600">{{ currentUser.age }}岁</span>
                <span class="text-gray-600">{{ currentUser.gender }}</span>
              </div>
            </div>
            <button 
              class="btn btn-primary btn-sm"
              @click="refreshMatches"
              :disabled="isLoading"
            >
              <span v-if="isLoading" class="loading loading-spinner loading-xs"></span>
              {{ isLoading ? '匹配中...' : '🔄 重新匹配' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 匹配结果 -->
      <div v-if="matches.length > 0" class="space-y-6">
        <div 
          v-for="(match, index) in matches" 
          :key="match.id"
          class="card bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <div class="card-body">
            <div class="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-6">
              <!-- 排名标识 -->
              <div class="flex-shrink-0">
                <div 
                  class="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg"
                  :class="getRankColor(index)"
                >
                  {{ index + 1 }}
                </div>
              </div>

              <!-- 用户头像 -->
              <div class="avatar flex-shrink-0">
                <div class="w-20 h-20 rounded-full ring ring-purple-200 ring-offset-2">
                  <img :src="match.avatar" :alt="match.name" />
                </div>
              </div>

              <!-- 用户信息 -->
              <div class="flex-1 space-y-3">
                <div>
                  <h3 class="text-2xl font-bold text-gray-800">{{ match.name }}</h3>
                  <div class="flex flex-wrap items-center gap-2 mt-2">
                    <span 
                      class="badge text-white text-sm px-3 py-2"
                      :class="getMBTIColor(match.mbtiType)"
                    >
                      {{ match.mbtiType }} - {{ getMBTIName(match.mbtiType) }}
                    </span>
                    <span class="badge badge-outline">{{ match.age }}岁</span>
                    <span class="badge badge-outline">{{ match.gender }}</span>
                  </div>
                </div>

                <!-- 自我介绍 -->
                <p v-if="match.bio" class="text-gray-600 text-sm">{{ match.bio }}</p>

                <!-- 兴趣爱好 -->
                <div>
                  <h4 class="font-medium text-gray-700 mb-2">兴趣爱好：</h4>
                  <div class="flex flex-wrap gap-2">
                    <span 
                      v-for="interest in match.interests" 
                      :key="interest"
                      class="badge badge-ghost text-xs"
                      :class="currentUser.interests.includes(interest) ? 'badge-primary text-white' : ''"
                    >
                      {{ interest }}
                      <span v-if="currentUser.interests.includes(interest)" class="ml-1">✨</span>
                    </span>
                  </div>
                </div>

                <!-- 匹配原因 -->
                <div>
                  <h4 class="font-medium text-gray-700 mb-2">匹配原因：</h4>
                  <div class="space-y-1">
                    <div 
                      v-for="reason in match.matchReasons" 
                      :key="reason"
                      class="flex items-center space-x-2"
                    >
                      <span class="text-green-500">✓</span>
                      <span class="text-sm text-gray-600">{{ reason }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 匹配度 -->
              <div class="flex-shrink-0 text-center">
                <div class="relative">
                  <div 
                    class="radial-progress text-primary text-4xl font-bold"
                    :style="`--value:${match.matchScore}; --size:6rem; --thickness: 8px;`"
                  >
                    {{ match.matchScore }}%
                  </div>
                  <div class="mt-2 text-sm text-gray-600">匹配度</div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions justify-end mt-4">
              <button class="btn btn-outline btn-sm">
                💬 发送消息
              </button>
              <button class="btn btn-primary btn-sm">
                ⭐ 添加关注
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 无匹配结果 -->
      <div v-else-if="!isLoading" class="text-center py-12">
        <div class="text-6xl mb-4">😔</div>
        <h3 class="text-2xl font-bold text-gray-600 mb-4">暂无匹配结果</h3>
        <p class="text-gray-500 mb-6">当前用户数量较少，请稍后再试或邀请朋友加入</p>
        <button 
          class="btn btn-primary"
          @click="$router.push('/register')"
        >
          邀请朋友注册
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading && matches.length === 0" class="text-center py-12">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="mt-4 text-gray-600">正在为你寻找最佳匹配...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import LocalStorageManager from '../utils/localStorage.js'
import { getMBTIColor, getMBTIName } from '../utils/mbtiTypes.js'

const router = useRouter()

// 响应式数据
const currentUser = ref(null)
const matches = ref([])
const isLoading = ref(false)

// 获取排名颜色
const getRankColor = (index) => {
  const colors = [
    'bg-gradient-to-br from-yellow-400 to-orange-500', // 第1名 - 金色
    'bg-gradient-to-br from-gray-400 to-gray-500',     // 第2名 - 银色
    'bg-gradient-to-br from-orange-600 to-red-600'     // 第3名 - 铜色
  ]
  return colors[index] || 'bg-gradient-to-br from-purple-500 to-pink-500'
}

// 获取匹配结果
const getMatches = async () => {
  if (!currentUser.value) return

  isLoading.value = true
  
  try {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const matchResults = LocalStorageManager.getMatches(currentUser.value, 3)
    matches.value = matchResults
  } catch (error) {
    console.error('获取匹配结果失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 刷新匹配
const refreshMatches = () => {
  getMatches()
}

// 组件挂载时初始化
onMounted(() => {
  currentUser.value = LocalStorageManager.getCurrentUser()
  
  if (!currentUser.value) {
    // 如果没有当前用户，跳转到注册页面
    router.push('/register')
    return
  }
  
  // 获取匹配结果
  getMatches()
})
</script>
