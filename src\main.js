import { createApp } from 'vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import './style.css'
import App from './App.vue'

// 导入页面组件
import HomePage from './pages/HomePage.vue'
import RegisterPage from './pages/RegisterPage.vue'
import MatchesPage from './pages/MatchesPage.vue'
import UsersPage from './pages/UsersPage.vue'
import ProfilePage from './pages/ProfilePage.vue'
import MessagesPage from './pages/MessagesPage.vue'
import FollowsPage from './pages/FollowsPage.vue'
import StatsPage from './pages/StatsPage.vue'
import CloudBaseDemoPage from './pages/CloudBaseDemoPage.vue'
import TestPage from './pages/TestPage.vue'

// 定义路由
const routes = [
  { path: '/', component: HomePage },
  { path: '/register', component: RegisterPage },
  { path: '/matches', component: MatchesPage },
  { path: '/users', component: UsersPage },
  { path: '/profile', component: ProfilePage },
  { path: '/messages', component: MessagesPage },
  { path: '/follows', component: FollowsPage },
  { path: '/stats', component: StatsPage },
  { path: '/cloudbase-demo', component: CloudBaseDemoPage },
  { path: '/test', component: TestPage },
  { path: '/:pathMatch(.*)*', redirect: '/' } // 404重定向到首页
]

// 创建路由实例 - 使用hash模式避免静态托管时的刷新404问题
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 创建应用实例
const app = createApp(App)

// 使用路由
app.use(router)

// 挂载应用
app.mount('#app') 