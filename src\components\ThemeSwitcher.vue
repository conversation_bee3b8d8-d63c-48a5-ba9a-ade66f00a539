<template>
  <div class="dropdown dropdown-end">
    <!-- 主题切换按钮 -->
    <div 
      tabindex="0" 
      role="button" 
      class="btn btn-ghost btn-sm"
      :title="`当前主题: ${currentThemeInfo?.displayName}`"
    >
      <span class="text-lg">{{ currentThemeInfo?.icon || '🎨' }}</span>
      <span v-if="showLabel" class="ml-2 hidden sm:inline">{{ currentThemeInfo?.displayName }}</span>
    </div>
    
    <!-- 主题选择菜单 -->
    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-64 p-2 shadow-lg border">
      <li class="menu-title">
        <span class="text-sm font-bold">🎨 选择主题</span>
      </li>
      
      <div class="divider my-1"></div>
      
      <!-- 主题列表 -->
      <li v-for="theme in themes" :key="theme.name">
        <a 
          class="flex items-center justify-between p-3 rounded-lg transition-all duration-200"
          :class="{ 
            'bg-primary text-primary-content': currentTheme === theme.name,
            'hover:bg-base-200': currentTheme !== theme.name 
          }"
          @click="selectTheme(theme.name)"
        >
          <div class="flex items-center space-x-3">
            <span class="text-xl">{{ theme.icon }}</span>
            <div>
              <div class="font-medium">{{ theme.displayName }}</div>
              <div class="text-xs opacity-70">{{ getThemeDescription(theme.name) }}</div>
            </div>
          </div>
          
          <!-- 当前主题标识 -->
          <div v-if="currentTheme === theme.name" class="flex items-center">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
          </div>
          
          <!-- 主题预览色块 -->
          <div v-else class="flex space-x-1">
            <div 
              class="w-3 h-3 rounded-full bg-gradient-to-r"
              :class="theme.colors.primary"
            ></div>
            <div 
              class="w-3 h-3 rounded-full bg-gradient-to-r"
              :class="theme.colors.secondary"
            ></div>
            <div 
              class="w-3 h-3 rounded-full bg-gradient-to-r"
              :class="theme.colors.accent"
            ></div>
          </div>
        </a>
      </li>
      
      <div class="divider my-1"></div>
      
      <!-- 主题设置 -->
      <li>
        <div class="p-2">
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text text-sm">跟随系统主题</span>
              <input 
                type="checkbox" 
                v-model="followSystemTheme"
                class="toggle toggle-sm toggle-primary" 
                @change="handleSystemThemeToggle"
              />
            </label>
          </div>
        </div>
      </li>
      
      <!-- 快捷键提示 -->
      <li>
        <div class="p-2 text-xs text-base-content/60 text-center">
          快捷键: Ctrl+T
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ThemeManager, THEMES } from '../utils/theme.js'

// Props
const props = defineProps({
  showLabel: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const currentTheme = ref('light')
const followSystemTheme = ref(false)
const themes = THEMES

// 计算属性
const currentThemeInfo = computed(() => {
  return ThemeManager.getThemeInfo(currentTheme.value)
})

// 方法
const selectTheme = (themeName) => {
  currentTheme.value = themeName
  ThemeManager.setTheme(themeName)
  followSystemTheme.value = false
  saveSettings()
}

const getThemeDescription = (themeName) => {
  const descriptions = {
    light: '清新明亮，适合日间使用',
    dark: '护眼深色，适合夜间使用',
    sunset: '温暖橙红，营造温馨氛围',
    ocean: '清凉蓝绿，带来宁静感受',
    forest: '自然绿色，回归大自然'
  }
  return descriptions[themeName] || '个性化主题'
}

const handleSystemThemeToggle = () => {
  if (followSystemTheme.value) {
    detectSystemTheme()
    watchSystemTheme()
  } else {
    unwatchSystemTheme()
  }
  saveSettings()
}

const detectSystemTheme = () => {
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    selectTheme('dark')
  } else {
    selectTheme('light')
  }
}

let systemThemeWatcher = null

const watchSystemTheme = () => {
  if (window.matchMedia) {
    systemThemeWatcher = window.matchMedia('(prefers-color-scheme: dark)')
    systemThemeWatcher.addEventListener('change', handleSystemThemeChange)
  }
}

const unwatchSystemTheme = () => {
  if (systemThemeWatcher) {
    systemThemeWatcher.removeEventListener('change', handleSystemThemeChange)
    systemThemeWatcher = null
  }
}

const handleSystemThemeChange = (e) => {
  if (followSystemTheme.value) {
    selectTheme(e.matches ? 'dark' : 'light')
  }
}

const saveSettings = () => {
  try {
    localStorage.setItem('mbti_friends_theme_settings', JSON.stringify({
      followSystemTheme: followSystemTheme.value,
      currentTheme: currentTheme.value
    }))
  } catch (error) {
    console.error('保存主题设置失败:', error)
  }
}

const loadSettings = () => {
  try {
    const settings = localStorage.getItem('mbti_friends_theme_settings')
    if (settings) {
      const parsed = JSON.parse(settings)
      followSystemTheme.value = parsed.followSystemTheme || false
      currentTheme.value = parsed.currentTheme || ThemeManager.getCurrentTheme()
      
      if (followSystemTheme.value) {
        detectSystemTheme()
        watchSystemTheme()
      }
    } else {
      currentTheme.value = ThemeManager.getCurrentTheme()
    }
  } catch (error) {
    currentTheme.value = ThemeManager.getCurrentTheme()
  }
}

// 全局快捷键处理
const handleGlobalKeydown = (event) => {
  if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 't') {
    event.preventDefault()
    ThemeManager.nextTheme()
    currentTheme.value = ThemeManager.getCurrentTheme()
  }
}

// 监听主题变化
watch(currentTheme, (newTheme) => {
  ThemeManager.setTheme(newTheme)
})

// 生命周期
onMounted(() => {
  loadSettings()
  document.addEventListener('keydown', handleGlobalKeydown)
})

// 清理
const cleanup = () => {
  unwatchSystemTheme()
  document.removeEventListener('keydown', handleGlobalKeydown)
}

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(cleanup)
</script>

<style scoped>
/* 主题预览动画 */
.dropdown-content a:hover .w-3 {
  transform: scale(1.2);
  transition: transform 0.2s ease;
}

/* 自定义滚动条 */
.dropdown-content {
  max-height: 400px;
  overflow-y: auto;
}

.dropdown-content::-webkit-scrollbar {
  width: 4px;
}

.dropdown-content::-webkit-scrollbar-track {
  background: transparent;
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
