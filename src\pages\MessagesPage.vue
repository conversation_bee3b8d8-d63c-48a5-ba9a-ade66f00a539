<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-100 py-8">
    <div class="container mx-auto px-4">
      <!-- 导航栏 -->
      <div class="flex justify-between items-center mb-8">
        <button 
          class="btn btn-ghost btn-sm"
          @click="$router.push('/')"
        >
          ← 返回首页
        </button>
        
        <div class="text-center">
          <h1 class="text-3xl font-bold">
            <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              💬 消息中心
            </span>
          </h1>
        </div>
        
        <button 
          class="btn btn-outline btn-sm"
          @click="$router.push('/profile')"
        >
          我的资料
        </button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 消息列表 -->
        <div class="lg:col-span-1">
          <div class="card bg-white/80 backdrop-blur-sm shadow-xl">
            <div class="card-body">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">对话列表</h3>
                <button 
                  class="btn btn-primary btn-sm"
                  @click="showNewMessage = true"
                >
                  ✏️ 新消息
                </button>
              </div>

              <!-- 搜索框 -->
              <div class="form-control mb-4">
                <input 
                  type="text"
                  v-model="searchQuery"
                  placeholder="搜索对话..."
                  class="input input-bordered input-sm"
                />
              </div>

              <!-- 对话列表 -->
              <div class="space-y-2 max-h-96 overflow-y-auto">
                <div 
                  v-for="conversation in filteredConversations" 
                  :key="conversation.id"
                  class="flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors"
                  :class="selectedConversation?.id === conversation.id ? 'bg-purple-100' : 'hover:bg-gray-50'"
                  @click="selectConversation(conversation)"
                >
                  <div class="avatar">
                    <div class="w-12 h-12 rounded-full relative">
                      <img :src="conversation.otherUser.avatar" :alt="conversation.otherUser.name" />
                      <div 
                        v-if="conversation.otherUser.isOnline"
                        class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"
                      ></div>
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex justify-between items-center">
                      <h4 class="font-medium text-gray-800 truncate">{{ conversation.otherUser.name }}</h4>
                      <span class="text-xs text-gray-500">{{ formatTime(conversation.lastMessage.timestamp) }}</span>
                    </div>
                    <p class="text-sm text-gray-600 truncate">{{ conversation.lastMessage.content }}</p>
                    <div v-if="conversation.unreadCount > 0" class="badge badge-error badge-sm text-white mt-1">
                      {{ conversation.unreadCount }}
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="conversations.length === 0" class="text-center py-8">
                <div class="text-4xl mb-4">💬</div>
                <p class="text-gray-600">还没有任何对话</p>
                <button 
                  class="btn btn-primary btn-sm mt-4"
                  @click="showNewMessage = true"
                >
                  开始第一个对话
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="lg:col-span-2">
          <div class="card bg-white/80 backdrop-blur-sm shadow-xl h-[600px] flex flex-col">
            <div v-if="selectedConversation" class="flex flex-col h-full">
              <!-- 聊天头部 -->
              <div class="card-body border-b border-gray-200 pb-4">
                <div class="flex items-center space-x-4">
                  <div class="avatar">
                    <div class="w-12 h-12 rounded-full relative">
                      <img :src="selectedConversation.otherUser.avatar" :alt="selectedConversation.otherUser.name" />
                      <div 
                        v-if="selectedConversation.otherUser.isOnline"
                        class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"
                      ></div>
                    </div>
                  </div>
                  <div class="flex-1">
                    <h3 class="font-bold text-gray-800">{{ selectedConversation.otherUser.name }}</h3>
                    <p class="text-sm text-gray-600">
                      {{ selectedConversation.otherUser.isOnline ? '在线' : '离线' }}
                    </p>
                  </div>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                      </svg>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                      <li><a @click="viewUserProfile(selectedConversation.otherUser)">查看资料</a></li>
                      <li><a @click="deleteConversation(selectedConversation.id)">删除对话</a></li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- 消息列表 -->
              <div class="flex-1 overflow-y-auto p-4 space-y-4" ref="messagesContainer">
                <div 
                  v-for="message in selectedConversation.messages" 
                  :key="message.id"
                  class="flex"
                  :class="message.senderId === currentUser.id ? 'justify-end' : 'justify-start'"
                >
                  <div 
                    class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg"
                    :class="message.senderId === currentUser.id 
                      ? 'bg-purple-500 text-white' 
                      : 'bg-gray-200 text-gray-800'"
                  >
                    <p class="text-sm">{{ message.content }}</p>
                    <p class="text-xs mt-1 opacity-70">{{ formatTime(message.timestamp) }}</p>
                  </div>
                </div>
              </div>

              <!-- 消息输入 -->
              <div class="card-body border-t border-gray-200 pt-4">
                <form @submit.prevent="sendMessage" class="flex space-x-2">
                  <input 
                    type="text"
                    v-model="newMessage"
                    placeholder="输入消息..."
                    class="input input-bordered flex-1"
                    :disabled="isSending"
                  />
                  <button 
                    type="submit"
                    class="btn btn-primary"
                    :disabled="!newMessage.trim() || isSending"
                  >
                    <span v-if="isSending" class="loading loading-spinner loading-sm"></span>
                    <span v-else>发送</span>
                  </button>
                </form>
              </div>
            </div>

            <!-- 未选择对话 -->
            <div v-else class="flex-1 flex items-center justify-center">
              <div class="text-center">
                <div class="text-6xl mb-4">💬</div>
                <h3 class="text-xl font-bold text-gray-600 mb-2">选择一个对话开始聊天</h3>
                <p class="text-gray-500">或者创建一个新的对话</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 新消息模态框 -->
      <div v-if="showNewMessage" class="modal modal-open">
        <div class="modal-box">
          <h3 class="font-bold text-lg mb-4">发送新消息</h3>
          
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">选择接收者</span>
            </label>
            <select 
              v-model="selectedRecipient"
              class="select select-bordered"
            >
              <option value="">请选择用户</option>
              <option 
                v-for="user in availableUsers" 
                :key="user.id"
                :value="user"
              >
                {{ user.name }} ({{ user.mbtiType }})
              </option>
            </select>
          </div>

          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">消息内容</span>
            </label>
            <textarea 
              v-model="newMessageContent"
              class="textarea textarea-bordered h-24"
              placeholder="输入你想说的话..."
            ></textarea>
          </div>

          <div class="modal-action">
            <button 
              class="btn btn-ghost"
              @click="closeNewMessage"
            >
              取消
            </button>
            <button 
              class="btn btn-primary"
              @click="createNewConversation"
              :disabled="!selectedRecipient || !newMessageContent.trim()"
            >
              发送
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import LocalStorageManager from '../utils/localStorage.js'

const router = useRouter()

// 响应式数据
const currentUser = ref(null)
const conversations = ref([])
const selectedConversation = ref(null)
const newMessage = ref('')
const isSending = ref(false)
const searchQuery = ref('')
const showNewMessage = ref(false)
const selectedRecipient = ref('')
const newMessageContent = ref('')
const messagesContainer = ref(null)

// 计算属性
const filteredConversations = computed(() => {
  if (!searchQuery.value) return conversations.value
  
  return conversations.value.filter(conv => 
    conv.otherUser.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const availableUsers = computed(() => {
  if (!currentUser.value) return []
  
  const allUsers = LocalStorageManager.getAllUsers()
  return allUsers.filter(user => 
    user.id !== currentUser.value.id && 
    !conversations.value.some(conv => conv.otherUser.id === user.id)
  )
})

// 方法
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now - date
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  return date.toLocaleDateString('zh-CN')
}

const selectConversation = (conversation) => {
  selectedConversation.value = conversation
  // 标记为已读
  conversation.unreadCount = 0
  saveConversations()
  
  nextTick(() => {
    scrollToBottom()
  })
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const sendMessage = () => {
  if (!newMessage.value.trim() || !selectedConversation.value) return
  
  isSending.value = true
  
  const message = {
    id: Date.now().toString(),
    senderId: currentUser.value.id,
    content: newMessage.value.trim(),
    timestamp: new Date().toISOString()
  }
  
  selectedConversation.value.messages.push(message)
  selectedConversation.value.lastMessage = message
  
  // 模拟发送延迟
  setTimeout(() => {
    newMessage.value = ''
    isSending.value = false
    saveConversations()
    
    nextTick(() => {
      scrollToBottom()
    })
  }, 500)
}

const createNewConversation = () => {
  if (!selectedRecipient.value || !newMessageContent.value.trim()) return
  
  const message = {
    id: Date.now().toString(),
    senderId: currentUser.value.id,
    content: newMessageContent.value.trim(),
    timestamp: new Date().toISOString()
  }
  
  const conversation = {
    id: Date.now().toString(),
    otherUser: selectedRecipient.value,
    messages: [message],
    lastMessage: message,
    unreadCount: 0
  }
  
  conversations.value.unshift(conversation)
  selectedConversation.value = conversation
  saveConversations()
  closeNewMessage()
  
  nextTick(() => {
    scrollToBottom()
  })
}

const closeNewMessage = () => {
  showNewMessage.value = false
  selectedRecipient.value = ''
  newMessageContent.value = ''
}

const viewUserProfile = (user) => {
  console.log('查看用户资料:', user.name)
  // 这里可以跳转到用户详情页面
}

const deleteConversation = (conversationId) => {
  if (confirm('确定要删除这个对话吗？')) {
    conversations.value = conversations.value.filter(conv => conv.id !== conversationId)
    if (selectedConversation.value?.id === conversationId) {
      selectedConversation.value = null
    }
    saveConversations()
  }
}

const saveConversations = () => {
  if (currentUser.value) {
    localStorage.setItem(`conversations_${currentUser.value.id}`, JSON.stringify(conversations.value))
  }
}

const loadConversations = () => {
  if (currentUser.value) {
    const saved = localStorage.getItem(`conversations_${currentUser.value.id}`)
    if (saved) {
      conversations.value = JSON.parse(saved)
    } else {
      // 初始化一些示例对话
      initSampleConversations()
    }
  }
}

const initSampleConversations = () => {
  const allUsers = LocalStorageManager.getAllUsers()
  const otherUsers = allUsers.filter(user => user.id !== currentUser.value.id)
  
  if (otherUsers.length > 0) {
    const sampleUser = otherUsers[0]
    const sampleConversation = {
      id: Date.now().toString(),
      otherUser: sampleUser,
      messages: [
        {
          id: '1',
          senderId: sampleUser.id,
          content: `你好！我看到我们的 MBTI 匹配度很高，想和你聊聊 😊`,
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
        }
      ],
      lastMessage: {
        id: '1',
        senderId: sampleUser.id,
        content: `你好！我看到我们的 MBTI 匹配度很高，想和你聊聊 😊`,
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
      },
      unreadCount: 1
    }
    
    conversations.value = [sampleConversation]
    saveConversations()
  }
}

// 组件挂载时初始化
onMounted(() => {
  currentUser.value = LocalStorageManager.getCurrentUser()
  
  if (!currentUser.value) {
    router.push('/register')
    return
  }
  
  loadConversations()
})
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
