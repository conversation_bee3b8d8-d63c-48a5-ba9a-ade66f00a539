<template>
  <div class="relative" ref="searchContainer">
    <!-- 搜索输入框 -->
    <div class="relative">
      <input 
        type="text"
        v-model="searchQuery"
        :placeholder="placeholder"
        class="input input-bordered w-full pr-10"
        :class="inputClass"
        @input="handleInput"
        @focus="showSuggestions = true"
        @keydown="handleKeydown"
        ref="searchInput"
      />
      
      <!-- 搜索图标 -->
      <div class="absolute inset-y-0 right-0 flex items-center pr-3">
        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>

      <!-- 清除按钮 -->
      <button 
        v-if="searchQuery"
        class="absolute inset-y-0 right-8 flex items-center pr-2"
        @click="clearSearch"
      >
        <svg class="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- 搜索建议下拉框 -->
    <div 
      v-if="showSuggestions && (suggestions.length > 0 || searchQuery)"
      class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
    >
      <!-- 搜索建议列表 -->
      <div v-if="suggestions.length > 0" class="py-2">
        <div 
          v-for="(suggestion, index) in suggestions" 
          :key="`${suggestion.type}-${suggestion.text}`"
          class="flex items-center px-4 py-2 hover:bg-gray-50 cursor-pointer transition-colors"
          :class="{ 'bg-purple-50': selectedIndex === index }"
          @click="selectSuggestion(suggestion)"
          @mouseenter="selectedIndex = index"
        >
          <span class="text-lg mr-3">{{ suggestion.icon }}</span>
          <div class="flex-1">
            <div class="font-medium text-gray-800">{{ suggestion.text }}</div>
            <div v-if="suggestion.type === 'user' && suggestion.user" class="text-xs text-gray-500">
              {{ suggestion.user.mbtiType }} · {{ suggestion.user.age }}岁
            </div>
            <div v-else class="text-xs text-gray-500 capitalize">
              {{ getTypeLabel(suggestion.type) }}
            </div>
          </div>
          <div v-if="suggestion.type === 'history'" class="text-xs text-gray-400">
            历史记录
          </div>
        </div>
      </div>

      <!-- 无结果提示 -->
      <div v-else-if="searchQuery" class="py-8 text-center text-gray-500">
        <div class="text-4xl mb-2">🔍</div>
        <div class="text-sm">没有找到相关建议</div>
      </div>

      <!-- 搜索历史管理 -->
      <div v-if="hasSearchHistory && !searchQuery" class="border-t border-gray-100 px-4 py-2">
        <button 
          class="text-xs text-gray-500 hover:text-gray-700"
          @click="clearSearchHistory"
        >
          清除搜索历史
        </button>
      </div>

      <!-- 快捷键提示 -->
      <div class="border-t border-gray-100 px-4 py-2 bg-gray-50">
        <div class="text-xs text-gray-500 flex items-center justify-between">
          <span>按 Enter 搜索</span>
          <span>Ctrl+K 快速搜索</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { SearchSuggestionManager } from '../utils/theme.js'

// Props
const props = defineProps({
  placeholder: {
    type: String,
    default: '搜索用户、兴趣、标签...'
  },
  inputClass: {
    type: String,
    default: ''
  },
  users: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'search', 'select'])

// 响应式数据
const searchQuery = ref(props.modelValue)
const showSuggestions = ref(false)
const selectedIndex = ref(-1)
const searchContainer = ref(null)
const searchInput = ref(null)

// 计算属性
const suggestions = computed(() => {
  return SearchSuggestionManager.getSearchSuggestions(searchQuery.value, props.users)
})

const hasSearchHistory = computed(() => {
  return SearchSuggestionManager.getSearchHistory().length > 0
})

// 监听搜索查询变化
watch(searchQuery, (newValue) => {
  emit('update:modelValue', newValue)
})

watch(() => props.modelValue, (newValue) => {
  searchQuery.value = newValue
})

// 方法
const handleInput = () => {
  selectedIndex.value = -1
  showSuggestions.value = true
}

const handleKeydown = (event) => {
  if (!showSuggestions.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, suggestions.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (selectedIndex.value >= 0 && suggestions.value[selectedIndex.value]) {
        selectSuggestion(suggestions.value[selectedIndex.value])
      } else {
        performSearch()
      }
      break
    case 'Escape':
      showSuggestions.value = false
      selectedIndex.value = -1
      searchInput.value?.blur()
      break
  }
}

const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion.text
  showSuggestions.value = false
  selectedIndex.value = -1
  
  // 添加到搜索历史
  SearchSuggestionManager.addSearchHistory(suggestion.text)
  
  // 触发搜索
  performSearch()
  
  // 触发选择事件
  emit('select', suggestion)
}

const performSearch = () => {
  if (searchQuery.value.trim()) {
    SearchSuggestionManager.addSearchHistory(searchQuery.value.trim())
    emit('search', searchQuery.value.trim())
    showSuggestions.value = false
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  showSuggestions.value = false
  selectedIndex.value = -1
  searchInput.value?.focus()
}

const clearSearchHistory = () => {
  SearchSuggestionManager.clearSearchHistory()
  showSuggestions.value = false
}

const getTypeLabel = (type) => {
  const labels = {
    history: '历史记录',
    user: '用户',
    interest: '兴趣',
    tag: '标签',
    mbti: 'MBTI类型'
  }
  return labels[type] || type
}

// 点击外部关闭建议框
const handleClickOutside = (event) => {
  if (searchContainer.value && !searchContainer.value.contains(event.target)) {
    showSuggestions.value = false
    selectedIndex.value = -1
  }
}

// 全局快捷键处理
const handleGlobalKeydown = (event) => {
  if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'k') {
    event.preventDefault()
    searchInput.value?.focus()
    searchInput.value?.select()
    showSuggestions.value = true
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleGlobalKeydown)
})

// 暴露方法给父组件
defineExpose({
  focus: () => searchInput.value?.focus(),
  clear: clearSearch
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
